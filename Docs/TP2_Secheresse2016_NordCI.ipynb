{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# TP 2 : Modélisation de la sécheresse agricole en Côte d'Ivoire\n\n## Cas de l'événement 2016 (Nord ivoirien) — Formation CLIMADA DGE Côte d'Ivoire\n\n**Objectifs du TP :**\n- Créer un hazard sécheresse à partir de données météo mensuelles de 2016\n- Modéliser l'exposition agricole régionale (coton, riz, maïs)\n- Calculer l'impact économique de la sécheresse par culture\n- Comparer les résultats à la réalité rapportée\n- Analyse de scénarios d'adaptation (variétés tolérantes, micro-irrigation, assurance)\n\n**Durée estimée :** 3h\n\n**Données utilisées :**\n- Précipitations, SPI et anomalies température 2016 (KORHOGO, BOUAKE, BONDOUKOU, FERKESSEDOUGOU)\n- Valeur agricole par culture/zone\n- Exposition: [exposition_agricole_nord_ci.csv]\n- Aléa: [secheresse_agriculture_2016.csv]\n\n---\n\n## 1. Configuration de l'environnement et import des bibliothèques\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "from climada.hazard import Hazard\n", "from climada.entity import Exposures, ImpactFunc, ImpactFuncSet\n", "from climada.engine import Impact\n", "from climada.util.coordinates import get_grid_points\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Importation et exploration des données agro-météorologiques 2016\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importation des données\n", "drought_df = pd.read_csv('secheresse_agriculture_2016.csv', parse_dates=['date'])\n", "print('Stations disponibles:', drought_df['station'].unique())\n", "drought_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyse SPI mensuel\n", "Affichons l'évolution du SPI sur 4 stations principales et identifions la période la plus sèche.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(10,6))\n", "for station in drought_df['station'].unique():\n", "    data = drought_df[drought_df['station']==station]\n", "    ax.plot(data['date'], data['spi_3m'], marker='o', label=station)\n", "ax.axhline(y=-1, color='red', linestyle='--', label='<PERSON><PERSON> sécheresse modérée')\n", "ax.axhline(y=-1.5, color='orange', linestyle='--', label='Sécheresse sévère')\n", "ax.axhline(y=-2, color='purple', linestyle='-.', label='Sécheresse extrême')\n", "plt.title('Évolution SPI 3 mois en 2016 (Nord CI)')\n", "plt.ylabel('SPI')\n", "plt.legend()\n", "plt.grid(alpha=0.3)\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Création de l'aléa sécheresse avec CLIMADA\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Centroids région Nord, grille 10 km\n", "north_bounds = {'min_lat': 7.5, 'max_lat': 10.5, 'min_lon': -7.5, 'max_lon': -2.5}\n", "centroids_nord = get_grid_points(north_bounds['min_lat'], north_bounds['min_lon'],\n    north_bounds['max_lat'], north_bounds['max_lon'], res_km=10)\n", "n_centroids = len(centroids_nord)\n", "\n", "# Sélection des sécheresses modérées à extrêmes\n", "drought_events = drought_df[drought_df['spi_3m']<=-1]\n", "n_events = len(drought_events)\n", "\n", "from scipy import sparse\n", "# Création matrice intensité\n", "intensity_matrix = np.zeros((n_events, n_centroids))\n", "# Attribution SPI par station aux centroids géographiquement proches\n", "for i, (_, evt) in enumerate(drought_events.iterrows()):\n", "    dist = [np.sqrt((evt['latitude']-lat)**2+(evt['longitude']-lon)**2) for lat,lon in centroids_nord]\n", "    weights = np.exp(-np.array(dist)/1.0)\n", "    intensity_matrix[i] = evt['spi_3m'] * weights / weights.sum()\n", "\n", "hazard_drought = Hazard()\n", "hazard_drought.tag = {'haz_type': 'DR', 'description': 'Sécheresse Nord 2016'}\n", "hazard_drought.units = 'SPI'\n", "hazard_drought.centroids.set_lat_lon(centroids_nord[:,1], centroids_nord[:,0])\n", "hazard_drought.event_id = np.arange(1, n_events+1)\n", "hazard_drought.date = drought_events['date'].values\n", "hazard_drought.frequency = [1] * n_events\n", "hazard_drought.intensity = sparse.csr_matrix(intensity_matrix)\n", "hazard_drought.check()\n", "print('✅ Hazard sécheresse créé')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Modélisation de l'exposition agricole\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["expo_df = pd.read_csv('exposition_agricole_nord_ci.csv')\n", "expo_df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Création GeoDataFrame Exposures pour CLIMADA\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["geometry = [Point(np.random.uniform(north_bounds['min_lon'], north_bounds['max_lon']),\n    np.random.uniform(north_bounds['min_lat'], north_bounds['max_lat'])) for _ in range(len(expo_df))]\n", "expo_gdf = gpd.GeoDataFrame(expo_df, geometry=geometry)\n", "exposures_agri = Exposures(expo_gdf)\n", "exposures_agri.gdf['category_id'] = 2\n", "exposures_agri.gdf['region_id'] = np.arange(1,len(expo_df)+1)\n", "exposures_agri.gdf['impf_DR'] = 1\n", "print('Exposition agricole CLIMADA créée')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Définition de fonctions de dommage agricoles (exemple coton)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["impact_func_coton = ImpactFunc()\n", "impact_func_coton.id = 1\n", "impact_func_coton.name = 'Coton_sécheresse_CI'\n", "impact_func_coton.intensity_unit = 'SPI'\n", "impact_func_coton.haz_type = 'DR'\n", "intensities = np.array([0, 1, 1.5, 2, 2.5, 3, 4])\n", "losses = np.array([0, 0.1, 0.25, 0.4, 0.6, 0.75, 0.9])  # 90% perte rendement extrême\n", "impact_func_coton.intensity = intensities\n", "impact_func_coton.mdd = losses \n", "impact_func_coton.paa = np.ones_like(losses) \n", "impact_func_set = ImpactFuncSet()\n", "impact_func_set.append(impact_func_coton)\n", "impact_func_set.check()\n", "print('Fonction impact coton OK')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Calcul de l'impact économique\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["impact_coton = Impact()\n", "impact_coton.calc(exposures=exposures_agri, impact_funcs=impact_func_set, hazard=hazard_drought)\n", "print('<PERSON>te annuelle moyenne (coton):', int(impact_coton.aai_agg/1e6), 'millions FCFA')\n", "print('Impact max événement:', int(impact_coton.at_event.max()/1e6), 'millions FCFA')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Analyse et synthèse\n", "Comparez le total des dommages estimés à la valeur du secteur, commentez.\n\nProposez un scénario d'adaptation et ré-estimez l'impact.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 4}