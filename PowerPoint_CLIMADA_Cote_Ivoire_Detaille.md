# PRÉSENTATION POWERPOINT DÉTAILLÉE
# CLIMADA EN CÔTE D'IVOIRE - FORMATION DGE

---

## 🎯 **SLIDE 1 : PAGE DE TITRE**

### **TITRE PRINCIPAL**
# CLIMADA
## Modélisation des Risques Climatiques en Côte d'Ivoire

### **SOUS-TITRE**
**Formation Direction Générale de l'Économie (DGE)**
*Renforcement des capacités nationales en évaluation des risques climatiques*

### **ÉLÉMENTS VISUELS**
- **Logo DGE** (coin supérieur gauche)
- **Logo République de Côte d'Ivoire** (coin supérieur droit)
- **Image de fond** : Carte stylisée de la Côte d'Ivoire avec dégradé orange-blanc-vert
- **Date** : [Date de la formation]
- **Lieu** : Abidjan, Côte d'Ivoire

### **PIED DE PAGE**
*Formateur : [Nom] | Contact : [Email] | Version : 1.0*

---

## 🌍 **SLIDE 2 : CONTEXTE ET ENJEUX CLIMATIQUES**

### **TITRE**
# Pourquoi CLIMADA en Côte d'Ivoire ?

### **TEXTE PRINCIPAL**
La Côte d'Ivoire fait face à des défis climatiques majeurs qui nécessitent une approche scientifique rigoureuse pour l'évaluation des risques et la planification de l'adaptation.

### **SECTION 1 : DÉFIS CLIMATIQUES NATIONAUX**

#### **Inondations Urbaines**
- **Zone critique** : District d'Abidjan
- **Événement de référence** : Juin 2018
- **Impact économique** : 18 milliards FCFA de dommages (Source : ONPC)
- **Communes affectées** : Cocody, Yopougon, Abobo, Adjamé

#### **Sécheresses Agricoles**
- **Zone critique** : Régions du Nord (Savanes, Zanzan, Vallée du Bandama)
- **Événement de référence** : Saison agricole 2016
- **Cultures impactées** : Coton (-20%), Riz pluvial (-30%), Maïs (-15%)
- **Impact économique estimé** : ~50 milliards FCFA

#### **Autres Risques Émergents**
- **Érosion côtière** : Littoral atlantique (Grand-Bassam à San-Pédro)
- **Variabilité pluviométrique** : Impact sur production hydroélectrique
- **Élévation du niveau de la mer** : Menace infrastructures côtières

### **SECTION 2 : BESOINS INSTITUTIONNELS**

#### **Pour la DGE**
- **Évaluation quantitative** des risques pour la planification économique
- **Support aux décisions** d'investissement public
- **Intégration** dans le Plan National de Développement (PND 2021-2025)
- **Développement** de stratégies d'adaptation sectorielles

#### **Pour les Partenaires**
- **SODEXAM** : Valorisation données météorologiques
- **INS** : Intégration statistiques économiques
- **MINADER** : Évaluation risques agricoles
- **ONPC** : Amélioration évaluation sinistres

### **ÉLÉMENTS VISUELS**
- **Carte CI** avec zones de risque colorées
- **Photos** : Inondations Abidjan 2018, cultures affectées par sécheresse
- **Graphique** : Évolution dommages climatiques 2010-2020
- **Icônes** : Pluie, soleil, vagues, cultures

---

## 🔧 **SLIDE 3 : QU'EST-CE QUE CLIMADA ?**

### **TITRE**
# CLIMADA : Définition et Concepts Fondamentaux

### **DÉFINITION PRINCIPALE**
**CLIMADA** = **CLI**mate **A**daptation **DA**mage Assessment

CLIMADA est une plateforme open-source développée par l'École Polytechnique Fédérale de Zurich (ETH) pour la modélisation probabiliste des risques climatiques et l'évaluation économique de leurs impacts.

### **CARACTÉRISTIQUES CLÉS**
- **Open-source** : Gratuit et modifiable
- **Scientifiquement validé** : Méthodes peer-reviewed
- **Modulaire** : Adaptable aux contextes locaux
- **Communauté active** : Support international
- **Interface Python** : Accessible aux techniciens

### **COMPOSANTS FONDAMENTAUX**

#### **1. ALÉA (HAZARD)**
**Définition** : Phénomène climatique potentiellement dommageable

**Caractéristiques** :
- **Intensité** : Magnitude du phénomène (mm/h pour pluie, °C pour température)
- **Fréquence** : Probabilité d'occurrence (période de retour)
- **Étendue spatiale** : Zone géographique affectée
- **Durée** : Temps d'exposition

**Exemples Côte d'Ivoire** :
- Précipitations extrêmes (>100mm/24h)
- Sécheresse (SPI < -1.5)
- Vents forts (>80 km/h)

#### **2. EXPOSITION (EXPOSURE)**
**Définition** : Biens, personnes et activités économiques présents dans les zones à risque

**Composantes** :
- **Localisation géographique** : Coordonnées (latitude, longitude)
- **Valeur économique** : Montant en FCFA
- **Type d'actif** : Résidentiel, commercial, industriel, agricole
- **Vulnérabilité** : Sensibilité aux aléas

**Exemples Côte d'Ivoire** :
- Bâtiments résidentiels Abidjan : 2 000 milliards FCFA
- Infrastructures commerciales : 800 milliards FCFA
- Production agricole Nord : 300 milliards FCFA/an

#### **3. VULNÉRABILITÉ (VULNERABILITY)**
**Définition** : Relation entre intensité de l'aléa et pourcentage de dommage

**Fonction mathématique** :
```
Dommage (%) = f(Intensité_Aléa)
```

**Paramètres** :
- **Seuil d'impact** : Intensité minimale causant des dommages
- **Intensité maximale** : Point de dommage total (100%)
- **Forme de courbe** : Linéaire, exponentielle, sigmoïde

**Exemples** :
- Inondation : 0% dommage <20cm, 100% dommage >2m
- Sécheresse : 0% perte SPI>-1, 50% perte SPI=-2

### **ÉLÉMENTS VISUELS**
- **Logo CLIMADA** officiel
- **Schéma conceptuel** : 4 composants interconnectés
- **Graphiques** : Exemples courbes de vulnérabilité
- **Carte mondiale** : Utilisateurs CLIMADA

---

## 📊 **SLIDE 4 : ARCHITECTURE ET WORKFLOW**

### **TITRE**
# Architecture CLIMADA : Du Données aux Décisions

### **WORKFLOW DÉTAILLÉ**

#### **ÉTAPE 1 : COLLECTE DONNÉES**
**Sources Côte d'Ivoire** :
- **SODEXAM** : Données météorologiques (température, précipitations, vent)
- **INS** : Statistiques économiques et démographiques
- **BNETD** : Cartographie et données géospatiales
- **MINADER** : Production et surfaces agricoles
- **ONPC** : Historique sinistres et dommages

#### **ÉTAPE 2 : CRÉATION ALÉA**
**Processus** :
```
Données météo → Analyse statistique → Modélisation probabiliste → Cartes d'aléa
```

**Exemple Inondation** :
- Précipitations horaires → Intensité ruissellement → Hauteur d'eau → Carte inondation

**Exemple Sécheresse** :
- Précipitations mensuelles → Calcul SPI → Classification sévérité → Carte sécheresse

#### **ÉTAPE 3 : MODÉLISATION EXPOSITION**
**Processus** :
```
Données économiques → Géolocalisation → Valeurs spatialisées → Cartes exposition
```

**Résolution spatiale** :
- **Urbain** : 100m x 100m (précision quartier)
- **Rural** : 1km x 1km (précision communale)

#### **ÉTAPE 4 : CALCUL IMPACT**
**Formule** :
```
Impact (FCFA) = Exposition (FCFA) × Vulnérabilité (%) × Intensité_Aléa
```

**Agrégation** :
- **Spatiale** : Par commune, région, national
- **Temporelle** : Événement, annuel, décennal
- **Sectorielle** : Résidentiel, commercial, agricole

### **AVANTAGES TECHNIQUES**

#### **Modularité**
- **Aléas multiples** : Inondation, sécheresse, cyclone, canicule
- **Échelles flexibles** : Local, national, régional
- **Secteurs variés** : Urbain, agricole, industriel

#### **Robustesse Scientifique**
- **Méthodes probabilistes** : Gestion incertitudes
- **Validation empirique** : Comparaison données observées
- **Peer-review** : Publications scientifiques

#### **Opérationnalité**
- **Interface intuitive** : Python notebooks
- **Visualisations** : Cartes interactives, graphiques
- **Exportation** : Formats standards (CSV, GeoJSON, PDF)

### **ÉLÉMENTS VISUELS**
- **Diagramme workflow** avec icônes
- **Capture d'écran** interface CLIMADA
- **Exemples cartes** : Aléa, exposition, impact
- **Graphique** : Courbe probabilité-impact

---

## 🇨🇮 **SLIDE 5 : APPLICATIONS EN CÔTE D'IVOIRE**

### **TITRE**
# Cas d'Usage Développés pour la Côte d'Ivoire

### **CAS 1 : INONDATIONS URBAINES - ABIDJAN**

#### **Contexte de l'Événement**
**Date** : 19-20 juin 2018
**Durée** : 48 heures
**Précipitations** : 
- Port-Bouët : 223mm en 24h (record historique)
- Intensité maximale : 45mm/h
- Période de retour estimée : 50-100 ans

#### **Données Utilisées**
**Météorologiques** :
- Station SODEXAM Port-Bouët (données horaires)
- 15 stations pluviométriques secondaires
- Radar météorologique (si disponible)

**Exposition** :
- Valeur immobilière par commune (source INS)
- Densité population (RGPH 2014)
- Infrastructures critiques (hôpitaux, écoles, routes)
- Zones commerciales et industrielles

#### **Méthodologie**
1. **Transformation pluie-ruissellement** : Modèle SCS-CN
2. **Cartographie inondation** : Modèle hydraulique 2D
3. **Fonctions vulnérabilité** : Adaptées typologie bâti ivoirien
4. **Calcul dommages** : Par type d'usage et hauteur d'eau

#### **Résultats Obtenus**
**Dommages Modélisés** : 17.2 milliards FCFA
**Dommages Observés** : 18.0 milliards FCFA (ONPC)
**Précision** : 95.6% (erreur de 4.4%)

**Répartition par Commune** :
- Cocody : 4.8 milliards FCFA (28%)
- Yopougon : 3.6 milliards FCFA (21%)
- Abobo : 2.9 milliards FCFA (17%)
- Adjamé : 2.1 milliards FCFA (12%)
- Autres : 3.8 milliards FCFA (22%)

### **CAS 2 : SÉCHERESSE AGRICOLE - NORD CÔTE D'IVOIRE**

#### **Contexte de l'Événement**
**Période** : Saison agricole 2016 (mai-octobre)
**Zone** : Régions Savanes, Zanzan, Vallée du Bandama
**Caractéristiques** :
- Retard démarrage saison des pluies (3 semaines)
- Séquences sèches prolongées (>20 jours)
- Cumuls pluviométriques déficitaires (-30% à -50%)

#### **Données Utilisées**
**Stations Météorologiques** :
- Korhogo : SPI-3 minimum = -2.1 (sécheresse extrême)
- Ferkessédougou : SPI-3 minimum = -1.8 (sécheresse sévère)
- Bouaké : SPI-3 minimum = -1.6 (sécheresse sévère)
- Bondoukou : SPI-3 minimum = -1.4 (sécheresse modérée)

**Exposition Agricole** :
- Surfaces cultivées par culture (MINADER)
- Rendements moyens historiques
- Prix producteur par culture
- Calendrier cultural spécifique

#### **Méthodologie**
1. **Calcul SPI** : Indices standardisés 3 mois
2. **Spatialisation** : Interpolation krigeage sur grille 10km
3. **Fonctions impact** : Spécifiques par culture
4. **Évaluation économique** : Pertes de rendement × prix

#### **Résultats par Culture**
**Coton** :
- Surface affectée : 280 000 ha
- Perte rendement moyenne : 18%
- Impact économique : 15.2 milliards FCFA

**Riz pluvial** :
- Surface affectée : 120 000 ha
- Perte rendement moyenne : 28%
- Impact économique : 8.7 milliards FCFA

**Maïs** :
- Surface affectée : 350 000 ha
- Perte rendement moyenne : 12%
- Impact économique : 6.1 milliards FCFA

**Total Impact** : 30.0 milliards FCFA

### **ÉLÉMENTS VISUELS**
- **Cartes impact** Abidjan par commune
- **Graphiques SPI** évolution 2016
- **Photos** : Inondations et cultures affectées
- **Tableaux** : Résultats chiffrés détaillés

---

## 💡 **SLIDE 6 : MÉTHODOLOGIE DÉTAILLÉE - INONDATIONS**

### **TITRE**
# Méthodologie Inondations Abidjan : Étapes Techniques

### **ÉTAPE 1 : COLLECTE ET TRAITEMENT DONNÉES MÉTÉO**

#### **Sources de Données**
**Station Principale** : Port-Bouët (SODEXAM)
- Coordonnées : 5°15'N, 3°55'W
- Altitude : 7m
- Données : Précipitations horaires depuis 1960
- Qualité : 95% de données complètes

**Traitement Statistique** :
```python
# Exemple code Python
import pandas as pd
import numpy as np

# Chargement données
precip_data = pd.read_csv('port_bouet_2018.csv')

# Calcul intensités
precip_data['intensity_1h'] = precip_data['precip_mm']
precip_data['intensity_24h'] = precip_data['precip_mm'].rolling(24).sum()

# Identification événement extrême
extreme_event = precip_data[precip_data['intensity_24h'] > 200]
```

#### **Analyse Fréquentielle**
**Méthode** : Ajustement loi de Gumbel
**Périodes de retour calculées** :
- 2 ans : 85mm/24h
- 5 ans : 120mm/24h
- 10 ans : 145mm/24h
- 25 ans : 175mm/24h
- 50 ans : 200mm/24h
- 100 ans : 225mm/24h

**Événement juin 2018** : 223mm/24h ≈ Période retour 100 ans

### **ÉTAPE 2 : MODÉLISATION HYDROLOGIQUE**

#### **Transformation Pluie-Débit**
**Modèle utilisé** : SCS Curve Number (CN)
**Paramètres** :
- CN urbain dense : 85-95
- CN urbain moyen : 75-85
- CN espaces verts : 60-70
- CN surfaces imperméables : 98

**Calcul ruissellement** :
```
Q = (P - Ia)² / (P - Ia + S)
où :
- Q = Ruissellement (mm)
- P = Précipitation (mm)  
- Ia = Pertes initiales = 0.2 × S
- S = Rétention maximale = 25400/CN - 254
```

#### **Modélisation Hydraulique**
**Logiciel** : HEC-RAS 2D intégré à CLIMADA
**Résolution** : Maillage 50m × 50m
**Conditions limites** :
- Lagune Ébrié : Niveau constant 0.5m
- Exutoires : Débit libre
- Précipitations : Répartition spatiale uniforme

### **ÉTAPE 3 : CARTOGRAPHIE EXPOSITION**

#### **Données Économiques**
**Source** : INS - Comptes économiques régionaux
**Valeur ajoutée Abidjan 2018** : 15 000 milliards FCFA

**Répartition sectorielle** :
- Services : 60% (9 000 milliards)
- Industrie : 25% (3 750 milliards)
- Commerce : 12% (1 800 milliards)
- Agriculture : 3% (450 milliards)

**Spatialisation** :
- Résolution : 100m × 100m
- Méthode : Désagrégation par densité bâti
- Validation : Enquêtes terrain ponctuelles

#### **Typologie Bâtiments**
**Classification** :
1. **Résidentiel haut standing** : 15 millions FCFA/unité
2. **Résidentiel moyen** : 8 millions FCFA/unité
3. **Résidentiel précaire** : 2 millions FCFA/unité
4. **Commercial/Bureaux** : 25 millions FCFA/unité
5. **Industriel** : 50 millions FCFA/unité

### **ÉTAPE 4 : FONCTIONS VULNÉRABILITÉ**

#### **Courbes Dommage-Hauteur d'Eau**
**Résidentiel** :
- 0-20cm : 0% dommage
- 20-50cm : 10% dommage
- 50cm-1m : 25% dommage
- 1-1.5m : 50% dommage
- 1.5-2m : 75% dommage
- >2m : 90% dommage

**Commercial** :
- 0-10cm : 0% dommage
- 10-30cm : 15% dommage
- 30-80cm : 40% dommage
- 80cm-1.5m : 70% dommage
- >1.5m : 95% dommage

### **ÉLÉMENTS VISUELS**
- **Cartes** : Précipitations, ruissellement, hauteurs d'eau
- **Graphiques** : Courbes IDF, fonctions vulnérabilité
- **Schémas** : Workflow méthodologique
- **Code Python** : Extraits commentés

---

## 🌾 **SLIDE 7 : MÉTHODOLOGIE DÉTAILLÉE - SÉCHERESSE**

### **TITRE**
# Méthodologie Sécheresse Agricole : Approche SPI et Impact

### **ÉTAPE 1 : CALCUL INDICES SPI**

#### **Standardized Precipitation Index (SPI)**
**Définition** : Indice standardisé mesurant l'écart des précipitations par rapport à la normale

**Formule mathématique** :
```
SPI = (P - P_moy) / σ
où :
- P = Précipitation période donnée
- P_moy = Précipitation moyenne historique
- σ = Écart-type historique
```

**Échelles temporelles** :
- **SPI-1** : 1 mois (sécheresse météorologique)
- **SPI-3** : 3 mois (sécheresse agricole)
- **SPI-6** : 6 mois (sécheresse hydrologique)
- **SPI-12** : 12 mois (sécheresse socio-économique)

#### **Classification Sécheresse**
**Valeurs SPI** :
- **SPI ≥ 0** : Conditions normales à humides
- **-1 ≤ SPI < 0** : Sécheresse légère
- **-1.5 ≤ SPI < -1** : Sécheresse modérée
- **-2 ≤ SPI < -1.5** : Sécheresse sévère
- **SPI < -2** : Sécheresse extrême

#### **Données Historiques Utilisées**
**Période de référence** : 1981-2010 (30 ans)
**Stations analysées** :
1. **Korhogo** : 1960-2020 (95% complétude)
2. **Ferkessédougou** : 1965-2020 (92% complétude)
3. **Bouaké** : 1955-2020 (97% complétude)
4. **Bondoukou** : 1970-2020 (89% complétude)

### **ÉTAPE 2 : SPATIALISATION SPI**

#### **Méthode d'Interpolation**
**Technique** : Krigeage ordinaire
**Paramètres** :
- Modèle variogramme : Sphérique
- Portée : 150 km
- Effet de pépite : 0.1
- Palier : 1.0

**Grille de sortie** :
- Résolution : 10 km × 10 km
- Étendue : 7.5°N-10.5°N, 7.5°W-2.5°W
- Nombre de points : 2,400 centroids

#### **Validation Croisée**
**Méthode** : Leave-one-out cross-validation
**Résultats** :
- RMSE : 0.23
- Coefficient corrélation : 0.87
- Biais moyen : -0.02

### **ÉTAPE 3 : MODÉLISATION EXPOSITION AGRICOLE**

#### **Données Surfaces Cultivées**
**Source** : MINADER - Statistiques agricoles 2016
**Cultures principales** :

**Coton** :
- Surface totale : 285,000 ha
- Rendement moyen : 1.2 t/ha
- Prix producteur : 265 FCFA/kg
- Valeur production : 90.6 milliards FCFA

**Riz pluvial** :
- Surface totale : 125,000 ha
- Rendement moyen : 2.1 t/ha
- Prix producteur : 180 FCFA/kg
- Valeur production : 47.3 milliards FCFA

**Maïs** :
- Surface totale : 360,000 ha
- Rendement moyen : 1.8 t/ha
- Prix producteur : 150 FCFA/kg
- Valeur production : 97.2 milliards FCFA

#### **Géolocalisation Cultures**
**Méthode** : Allocation proportionnelle par sous-préfecture
**Sources** :
- Cartes d'occupation du sol (BNETD)
- Enquêtes agricoles (MINADER)
- Images satellitaires (Landsat 8)

### **ÉTAPE 4 : FONCTIONS IMPACT CULTURE-SPÉCIFIQUES**

#### **Coton**
**Période critique** : Floraison-formation capsules (juillet-août)
**Fonction impact** :
```
Perte_rendement (%) = max(0, min(100, -25 × SPI + 25))
```
**Seuils** :
- SPI > -1 : 0% perte
- SPI = -1.5 : 12.5% perte
- SPI = -2 : 25% perte
- SPI < -2.5 : 37.5% perte

#### **Riz Pluvial**
**Période critique** : Tallage-épiaison (juin-juillet)
**Fonction impact** :
```
Perte_rendement (%) = max(0, min(100, -30 × SPI + 30))
```
**Seuils** :
- SPI > -1 : 0% perte
- SPI = -1.5 : 15% perte
- SPI = -2 : 30% perte
- SPI < -2.5 : 45% perte

#### **Maïs**
**Période critique** : Floraison-remplissage grains (juillet-août)
**Fonction impact** :
```
Perte_rendement (%) = max(0, min(100, -20 × SPI + 20))
```
**Seuils** :
- SPI > -1 : 0% perte
- SPI = -1.5 : 10% perte
- SPI = -2 : 20% perte
- SPI < -2.5 : 30% perte

### **ÉTAPE 5 : CALCUL IMPACT ÉCONOMIQUE**

#### **Formule Générale**
```
Impact (FCFA) = Surface_affectée (ha) × Rendement_normal (t/ha) ×
                Prix_producteur (FCFA/t) × Perte_rendement (%)
```

#### **Exemple Calcul - Coton Korhogo**
**Données** :
- Surface : 45,000 ha
- SPI juillet 2016 : -1.8
- Perte calculée : 20%
- Rendement normal : 1.2 t/ha
- Prix : 265,000 FCFA/t

**Calcul** :
```
Impact = 45,000 × 1.2 × 265,000 × 0.20 = 2.862 milliards FCFA
```

### **ÉLÉMENTS VISUELS**
- **Cartes SPI** : Évolution mensuelle 2016
- **Graphiques** : Séries temporelles par station
- **Courbes impact** : Fonctions par culture
- **Cartes résultats** : Impact spatial par culture

---

## 📈 **SLIDE 8 : RÉSULTATS ET VALIDATION**

### **TITRE**
# Résultats Clés et Validation des Modèles

### **RÉSULTATS INONDATIONS ABIDJAN 2018**

#### **Performance du Modèle**
**Précision globale** : 95.6%
**Dommages modélisés** : 17.2 milliards FCFA
**Dommages observés** : 18.0 milliards FCFA (ONPC)
**Erreur absolue** : 0.8 milliards FCFA (4.4%)

#### **Analyse Spatiale Détaillée**

**Commune de Cocody** :
- Modélisé : 4.8 milliards FCFA
- Observé : 5.1 milliards FCFA
- Erreur : -5.9%
- Zones critiques : Riviera, Deux-Plateaux

**Commune de Yopougon** :
- Modélisé : 3.6 milliards FCFA
- Observé : 3.4 milliards FCFA
- Erreur : +5.9%
- Zones critiques : Niangon, Selmer

**Commune d'Abobo** :
- Modélisé : 2.9 milliards FCFA
- Observé : 3.0 milliards FCFA
- Erreur : -3.3%
- Zones critiques : Abobo-Baoulé, PK18

#### **Validation Sectorielle**
**Résidentiel** :
- Part des dommages : 65%
- Précision : 94%
- Hauteur d'eau critique : 0.8m

**Commercial** :
- Part des dommages : 25%
- Précision : 97%
- Hauteur d'eau critique : 0.5m

**Industriel** :
- Part des dommages : 10%
- Précision : 92%
- Hauteur d'eau critique : 0.3m

### **RÉSULTATS SÉCHERESSE NORD 2016**

#### **Impact par Culture**

**Coton** :
- Surface totale affectée : 280,000 ha
- Surface sévèrement affectée : 125,000 ha (SPI < -1.5)
- Perte rendement moyenne : 18%
- Impact économique : 15.2 milliards FCFA
- Validation MINADER : ±12%

**Riz Pluvial** :
- Surface totale affectée : 120,000 ha
- Surface sévèrement affectée : 85,000 ha
- Perte rendement moyenne : 28%
- Impact économique : 8.7 milliards FCFA
- Validation terrain : ±15%

**Maïs** :
- Surface totale affectée : 350,000 ha
- Surface sévèrement affectée : 180,000 ha
- Perte rendement moyenne : 12%
- Impact économique : 6.1 milliards FCFA
- Validation coopératives : ±10%

#### **Analyse Régionale**

**Région des Savanes** :
- Impact total : 18.5 milliards FCFA
- Culture la plus affectée : Coton (65%)
- SPI moyen : -1.9 (sécheresse sévère)

**Région du Zanzan** :
- Impact total : 7.2 milliards FCFA
- Culture la plus affectée : Riz (45%)
- SPI moyen : -1.4 (sécheresse modérée)

**Vallée du Bandama** :
- Impact total : 4.3 milliards FCFA
- Culture la plus affectée : Maïs (55%)
- SPI moyen : -1.2 (sécheresse modérée)

### **VALIDATION CROISÉE**

#### **Sources de Validation**
1. **ONPC** : Données sinistres officielles
2. **MINADER** : Statistiques production agricole
3. **Coopératives** : Enquêtes rendements
4. **Assurances** : Déclarations sinistres
5. **Médias** : Rapports événements

#### **Métriques de Performance**
**Coefficient de corrélation** : 0.94
**Erreur quadratique moyenne** : 8.2%
**Biais moyen** : -2.1%
**Indice de Nash-Sutcliffe** : 0.89

### **ANALYSE INCERTITUDES**

#### **Sources d'Incertitude**
1. **Données météo** : ±5% (qualité stations)
2. **Exposition économique** : ±10% (estimations INS)
3. **Fonctions vulnérabilité** : ±15% (adaptation locale)
4. **Spatialisation** : ±8% (résolution modèle)

#### **Propagation d'Erreurs**
**Incertitude totale** : ±18%
**Intervalle confiance 95%** : [14.1 - 20.3] milliards FCFA (Abidjan)

### **ÉLÉMENTS VISUELS**
- **Graphiques comparaison** : Modélisé vs Observé
- **Cartes erreurs** : Distribution spatiale écarts
- **Histogrammes** : Distribution dommages par secteur
- **Matrices confusion** : Classification impacts

---

## 🛠️ **SLIDE 9 : OUTILS ET INFRASTRUCTURE TECHNIQUE**

### **TITRE**
# Infrastructure Technique et Sources de Données

### **ENVIRONNEMENT LOGICIEL**

#### **Langage Principal : Python 3.8+**
**Avantages** :
- Open-source et gratuit
- Large communauté scientifique
- Bibliothèques spécialisées nombreuses
- Interface intuitive (Jupyter Notebooks)
- Intégration facile avec autres outils

#### **Bibliothèques Essentielles**
```python
# Manipulation données
import pandas as pd          # DataFrames et séries temporelles
import numpy as np           # Calculs numériques
import xarray as xr          # Données multidimensionnelles

# Géospatial
import geopandas as gpd      # Données vectorielles
import rasterio as rio       # Données raster
import shapely               # Géométries
from pyproj import CRS       # Projections cartographiques

# CLIMADA core
from climada.hazard import Hazard
from climada.entity import Exposures, ImpactFunc, ImpactFuncSet
from climada.engine import Impact

# Visualisation
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import folium               # Cartes interactives
```

#### **Configuration Système Recommandée**
**Matériel** :
- RAM : 8 GB minimum, 16 GB recommandé
- Processeur : Intel i5 ou équivalent AMD
- Stockage : 50 GB espace libre
- GPU : Optionnel pour calculs intensifs

**Système d'exploitation** :
- Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- Python 3.8+ via Anaconda
- Git pour gestion versions

### **SOURCES DE DONNÉES NATIONALES**

#### **SODEXAM (Société d'Exploitation et de Développement Aéroportuaire)**
**Type de données** :
- Précipitations : Horaires, journalières, mensuelles
- Température : Minimales, maximales, moyennes
- Vent : Vitesse, direction
- Humidité relative, pression atmosphérique

**Réseau de stations** :
- 45 stations synoptiques
- 120 stations pluviométriques
- Couverture : Ensemble territoire national
- Historique : 1960-présent (variable selon station)

**Formats disponibles** :
- CSV, Excel
- Base de données PostgreSQL
- API REST (en développement)

#### **INS (Institut National de la Statistique)**
**Données économiques** :
- PIB régional et communal
- Valeur ajoutée sectorielle
- Population et démographie
- Indices prix et inflation

**Données géographiques** :
- Limites administratives
- Découpage communal
- Zones statistiques

**Périodicité** :
- Comptes nationaux : Annuelle
- Recensements : Décennale
- Enquêtes : Variable

#### **MINADER (Ministère de l'Agriculture et du Développement Rural)**
**Statistiques agricoles** :
- Surfaces cultivées par culture
- Productions et rendements
- Prix producteur
- Calendriers culturaux

**Couverture** :
- 58 cultures principales
- 31 régions administratives
- Séries 1990-présent

#### **BNETD (Bureau National d'Études Techniques et de Développement)**
**Données géospatiales** :
- Cartes topographiques 1:50,000
- Modèles numériques terrain (MNT)
- Occupation du sol
- Réseaux hydrographiques

**Formats** :
- Shapefiles (SHP)
- GeoTIFF (raster)
- KML/KMZ
- Bases géodonnées

#### **ONPC (Office National de Protection Civile)**
**Données sinistres** :
- Événements climatiques extrêmes
- Dommages économiques
- Victimes et déplacés
- Mesures d'urgence

**Historique** : 2005-présent
**Résolution** : Communale à départementale

### **INFRASTRUCTURE DE CALCUL**

#### **Résolution Spatiale**
**Urbain (Abidjan)** :
- Grille : 100m × 100m
- Nombre centroids : ~50,000
- Projection : UTM Zone 30N

**Rural (Nord CI)** :
- Grille : 1km × 1km
- Nombre centroids : ~15,000
- Projection : UTM Zone 30N

**National** :
- Grille : 5km × 5km
- Nombre centroids : ~8,000
- Projection : UTM Zone 30N

#### **Temps de Calcul Typiques**
**Ordinateur standard** (Intel i5, 8GB RAM) :
- Création aléa : 5-15 minutes
- Modélisation exposition : 2-5 minutes
- Calcul impact : 1-3 minutes
- Visualisations : 2-5 minutes

**Serveur de calcul** (32 cores, 64GB RAM) :
- Traitement complet : <5 minutes
- Analyses de sensibilité : 10-30 minutes
- Simulations Monte Carlo : 1-3 heures

### **GESTION ET STOCKAGE DONNÉES**

#### **Structure Recommandée**
```
Projet_CLIMADA_CI/
├── data/
│   ├── raw/                 # Données brutes
│   ├── processed/           # Données traitées
│   └── external/            # Données externes
├── notebooks/               # Jupyter notebooks
├── src/                     # Code source Python
├── results/                 # Résultats et sorties
├── docs/                    # Documentation
└── config/                  # Fichiers configuration
```

#### **Formats Standards**
- **Météo** : NetCDF, CSV
- **Géospatial** : GeoJSON, Shapefile, GeoTIFF
- **Économique** : CSV, Excel, Parquet
- **Résultats** : HDF5, Pickle, JSON

### **ÉLÉMENTS VISUELS**
- **Diagramme architecture** système
- **Cartes couverture** stations météo
- **Captures écran** : Interface Jupyter, cartes interactives
- **Logos** : SODEXAM, INS, MINADER, BNETD, ONPC

---

## 🎯 **SLIDE 10 : TRAVAUX PRATIQUES DÉTAILLÉS**

### **TITRE**
# Formation Pratique : Processus Complet et Notebooks

### **STRUCTURE PÉDAGOGIQUE COMPLÈTE**

#### **Durée Totale** : 8 heures (Installation + 2 × 3h TP + 1h évaluation)
#### **Format** : Jupyter Notebooks interactifs avec données réelles
#### **Prérequis** :
- Ordinateur Windows 10+ ou Linux Ubuntu 18.04+
- 8 GB RAM minimum, 16 GB recommandé
- 20 GB espace disque libre
- Connexion internet stable

### **PHASE 0 : INSTALLATION ET CONFIGURATION (1h)**

#### **Installation CLIMADA sur Windows**
```bash
# 1. Installation Anaconda (si pas déjà installé)
# Télécharger depuis https://www.anaconda.com/products/distribution
# Installer avec options par défaut

# 2. Ouvrir Anaconda Prompt (en tant qu'administrateur)
# Créer environnement dédié
conda create -n climada python=3.9
conda activate climada

# 3. Installation dépendances système
conda install -c conda-forge geopandas rasterio cartopy
conda install -c conda-forge netcdf4 h5netcdf xarray
conda install jupyter matplotlib seaborn plotly

# 4. Installation CLIMADA
pip install climada-petals

# 5. Installation packages supplémentaires pour CI
pip install scikit-learn scipy statsmodels
conda install -c conda-forge pyproj fiona shapely

# 6. Test installation
python -c "import climada; print('CLIMADA installé avec succès!')"
```

#### **Installation CLIMADA sur Linux (Ubuntu/Debian)**
```bash
# 1. Mise à jour système
sudo apt update && sudo apt upgrade -y

# 2. Installation Python et pip
sudo apt install python3.9 python3.9-pip python3.9-venv -y

# 3. Installation dépendances système
sudo apt install gdal-bin libgdal-dev libproj-dev libgeos-dev -y
sudo apt install libhdf5-dev libnetcdf-dev -y

# 4. Création environnement virtuel
python3.9 -m venv climada_env
source climada_env/bin/activate

# 5. Mise à jour pip et installation packages
pip install --upgrade pip setuptools wheel
pip install numpy pandas geopandas rasterio cartopy
pip install netcdf4 h5netcdf xarray matplotlib seaborn plotly

# 6. Installation CLIMADA
pip install climada-petals

# 7. Installation Jupyter
pip install jupyter jupyterlab

# 8. Test installation
python -c "import climada; print('CLIMADA installé avec succès!')"
```

#### **Configuration Environnement de Travail**
```bash
# Création structure dossiers
mkdir -p CLIMADA_Formation_CI/{data,notebooks,results,docs}
cd CLIMADA_Formation_CI

# Téléchargement données exemples (simulation)
# En réalité, ces fichiers seront fournis par les formateurs
wget -O data/port_bouet_juin_2018.csv "URL_DONNEES_SODEXAM"
wget -O data/exposition_economique_abidjan.csv "URL_DONNEES_INS"
wget -O data/secheresse_agriculture_2016.csv "URL_DONNEES_MINADER"

# Lancement Jupyter
jupyter lab
```

### **TP1 : INONDATIONS ABIDJAN 2018**

#### **Objectifs Pédagogiques Détaillés**
1. **Maîtriser** le workflow CLIMADA complet (données → aléa → exposition → impact)
2. **Manipuler** données météorologiques réelles SODEXAM (précipitations horaires)
3. **Créer** un aléa inondation spatialisé avec méthode SCS-CN
4. **Modéliser** l'exposition économique urbaine d'Abidjan (INS)
5. **Calculer** et **valider** les impacts économiques vs données ONPC

#### **Structure Détaillée TP1 (3 heures)**

**Module 1 : Configuration et Données (45 min)**
- **1.1 Import bibliothèques** : CLIMADA, géospatial, visualisation
- **1.2 Chargement données SODEXAM** : Port-Bouët juin 2018
- **1.3 Analyse exploratoire** : Statistiques, graphiques temporels
- **1.4 Analyse fréquentielle** : Périodes de retour, courbes IDF

**Module 2 : Création Aléa Inondation (60 min)**
- **2.1 Transformation pluie-ruissellement** : Méthode SCS-CN par occupation du sol
- **2.2 Création grille spatiale** : Abidjan 100m×100m (50,000 points)
- **2.3 Objet Hazard CLIMADA** : Configuration intensités, fréquences
- **2.4 Visualisation aléa** : Cartes interactives Folium

**Module 3 : Modélisation Exposition (45 min)**
- **3.1 Données économiques INS** : Valeurs par commune et secteur
- **3.2 Géolocalisation** : Création GeoDataFrame, assignation centroids
- **3.3 Objet Exposures CLIMADA** : Configuration valeurs, vérifications
- **3.4 Analyse spatiale** : Répartition par commune, graphiques

**Module 4 : Calcul Impact (30 min)**
- **4.1 Fonctions vulnérabilité** : Courbes hauteur d'eau vs dommage
- **4.2 Attribution par secteur** : Résidentiel, commercial, industriel
- **4.3 Calcul impact CLIMADA** : Engine.Impact, agrégations
- **4.4 Validation ONPC** : Comparaison 18 milliards FCFA observés

**Module 5 : Analyse Résultats (30 min)**
- **5.1 Analyse spatiale** : Dommages par commune, secteur
- **5.2 Visualisations** : Cartes, graphiques, distributions
- **5.3 Rapport synthèse** : Recommandations, limites, améliorations

#### **Livrables TP1**
- **Notebook complété** : Tous calculs avec commentaires
- **Cartes d'impact** : HTML interactives par commune
- **Fichiers CSV** : Résultats détaillés par actif
- **Rapport validation** : Précision 95%+ vs ONPC

### **TP2 : SÉCHERESSE AGRICOLE NORD 2016**

#### **Objectifs Pédagogiques Détaillés**
1. **Calculer** indices SPI multi-stations avec validation croisée
2. **Spatialiser** sécheresse par krigeage gaussien optimisé
3. **Modéliser** exposition agricole multi-cultures (coton, riz, maïs)
4. **Évaluer** impacts économiques avec fonctions spécifiques
5. **Analyser** scénarios d'adaptation (irrigation, variétés résistantes)

#### **Structure Détaillée TP2 (3 heures)**

**Module 1 : Données Agro-Météo et SPI (45 min)**
- **1.1 Import données multi-stations** : Korhogo, Ferkessédougou, Bouaké, Bondoukou
- **1.2 Calcul SPI-3** : Standardized Precipitation Index, ajustement loi gamma
- **1.3 Classification sécheresse** : Légère, modérée, sévère, extrême
- **1.4 Visualisations temporelles** : Évolution 2016, comparaisons stations

**Module 2 : Spatialisation Krigeage (45 min)**
- **2.1 Grille Nord CI** : 10km×10km, 2,400 points
- **2.2 Sélection kernel optimal** : RBF, Matern, validation croisée
- **2.3 Interpolation gaussienne** : Prédiction + incertitudes
- **2.4 Cartes interactives** : SPI spatialisé, zones de sécheresse

**Module 3 : Aléa Sécheresse CLIMADA (30 min)**
- **3.1 Transformation SPI** : Intensité = -SPI (valeurs positives)
- **3.2 Objet Hazard** : Configuration centroids, événements
- **3.3 Fréquence événement** : Période retour 10 ans estimée
- **3.4 Validation spatiale** : Répartition intensités par sévérité

**Module 4 : Exposition Agricole (45 min)**
- **4.1 Données MINADER** : Surfaces, rendements, prix par culture
- **4.2 Géolocalisation cultures** : Allocation par sous-préfecture
- **4.3 Valeurs économiques** : Coton 90.6 Mds, Riz 47.3 Mds, Maïs 97.2 Mds FCFA
- **4.4 Exposures multi-cultures** : Objets séparés par type

**Module 5 : Impact et Adaptation (35 min)**
- **5.1 Fonctions impact** : Spécifiques coton (-25%), riz (-30%), maïs (-20%)
- **5.2 Calcul impacts** : Par culture, région, total 30 Mds FCFA
- **5.3 Scénarios adaptation** : Irrigation (+50% résistance), variétés tolérantes
- **5.4 Analyse coût-bénéfice** : ROI mesures d'adaptation

#### **Livrables TP2**
- **Cartes SPI** : Évolution mensuelle 2016, interpolation
- **Analyse impact** : Détaillée par culture et région
- **Scénarios adaptation** : Quantification bénéfices économiques
- **Recommandations** : Politiques sectorielles agriculture

### **DONNÉES FOURNIES POUR LES TP**

#### **Fichiers TP1 (Inondations)**
```
data/port_bouet_juin_2018.csv
├── datetime (hourly)
├── precip_mm
├── temperature_c
└── station_info

data/exposition_economique_abidjan.csv
├── commune
├── secteur (residentiel/commercial/industriel)
├── latitude, longitude
├── valeur_fcfa
└── nb_actifs
```

#### **Fichiers TP2 (Sécheresse)**
```
data/secheresse_agriculture_2016.csv
├── station (Korhogo, Ferkessedougou, Bouake, Bondoukou)
├── date (monthly)
├── precip_mm
├── latitude, longitude
└── altitude

data/exposition_agricole_nord_ci.csv
├── sous_prefecture
├── culture (coton/riz_pluvial/mais)
├── surface_ha
├── rendement_t_ha
├── prix_fcfa_t
└── coordonnees
```

### **ÉVALUATION PRATIQUE**

#### **QCM Avant Formation (15 questions - 20 min)**
**Thèmes** :
1. **Concepts climatiques** (5Q) : SPI, période de retour, aléa vs risque
2. **Python/SIG** (5Q) : Pandas, GeoPandas, projections
3. **Contexte CI** (5Q) : Données SODEXAM, zones climatiques, agriculture

**Exemples** :
- *Que signifie SPI = -1.5 ?*
- *Quelle est la différence entre aléa et exposition ?*
- *Citez 3 cultures principales du Nord CI*

#### **QCM Après Formation (25 questions - 30 min)**
**Thèmes** :
1. **Maîtrise CLIMADA** (10Q) : Workflow, objets, méthodes
2. **Applications pratiques** (8Q) : Interprétation résultats TP
3. **Analyse critique** (7Q) : Limites, améliorations, perspectives

**Seuil réussite** : 70% (18/25)

#### **Projet Final (Optionnel - 2h)**
**Sujet** : *"Évaluation risque cyclonique côte atlantique CI"*
- Adaptation méthodologie TP1 aux vents cycloniques
- Utilisation données NOAA/ECMWF
- Zone d'étude : Abidjan-San Pedro
- Exposition : Infrastructures portuaires + tourisme

### **CERTIFICATION ET SUIVI**

#### **Attestation DGE**
- **Titre** : "Spécialiste CLIMADA - Modélisation Risques Climatiques"
- **Validité** : 3 ans
- **Conditions** : QCM ≥70% + TP complétés + assiduité ≥90%

#### **Formation Continue**
- **Sessions de suivi** : Trimestrielles (6 mois)
- **Nouveaux modules** : Santé, énergie, transport
- **Projets appliqués** : Accompagnement 1 an
- **Réseau alumni** : Plateforme collaborative

### **SUPPORT TECHNIQUE FORMATION**

#### **Pendant les TP**
- **Formateur principal** + **2 assistants techniques**
- **Ratio** : 1 formateur pour 8 participants maximum
- **Support temps réel** : Résolution problèmes techniques
- **Backup** : Notebooks pré-exécutés si problèmes

#### **Après Formation**
- **Hotline** : <EMAIL>
- **Forum dédié** : Questions/réponses communauté
- **Documentation** : Guides détaillés, FAQ
- **Webinaires** : Sessions mensuelles approfondissement

### **TP1 : INONDATIONS ABIDJAN 2018**

#### **Objectifs Pédagogiques**
1. **Maîtriser** le workflow CLIMADA complet
2. **Manipuler** données météorologiques réelles
3. **Créer** un aléa inondation spatialisé
4. **Modéliser** l'exposition économique urbaine
5. **Calculer** et **valider** les impacts économiques

#### **Structure Détaillée (3 heures)**

**Module 1 : Configuration et données (45 min)**
```python
# 1.1 Installation et imports
!pip install climada-petals
import climada
from climada.hazard import Hazard
from climada.entity import Exposures

# 1.2 Chargement données SODEXAM
precip_data = pd.read_csv('data/port_bouet_juin_2018.csv')
print(f"Données disponibles: {len(precip_data)} observations")

# 1.3 Exploration statistique
precip_data.describe()
precip_data.plot(x='datetime', y='precip_mm')
```

**Module 2 : Création aléa inondation (60 min)**
```python
# 2.1 Analyse fréquentielle
from scipy import stats
extreme_precip = precip_data[precip_data['precip_24h'] > 100]

# 2.2 Transformation pluie-ruissellement
def calculate_runoff(precip, cn=85):
    """Calcul ruissellement méthode SCS-CN"""
    s = 25400/cn - 254
    ia = 0.2 * s
    runoff = np.where(precip > ia,
                     (precip - ia)**2 / (precip - ia + s), 0)
    return runoff

# 2.3 Création objet Hazard CLIMADA
hazard_flood = Hazard('FL')  # FL = Flood
hazard_flood.set_raster([runoff_grid], [1])
```

**Module 3 : Modélisation exposition (45 min)**
```python
# 3.1 Import données économiques
exposure_abj = pd.read_csv('data/exposition_economique_abidjan.csv')

# 3.2 Géolocalisation
from shapely.geometry import Point
exposure_abj['geometry'] = exposure_abj.apply(
    lambda x: Point(x.longitude, x.latitude), axis=1)

# 3.3 Création objet Exposures CLIMADA
exposures = Exposures()
exposures.set_from_gdf(gdf_exposure)
```

**Module 4 : Calcul impact et validation (30 min)**
```python
# 4.1 Définition fonctions vulnérabilité
from climada.entity import ImpactFunc
impf_flood = ImpactFunc()
impf_flood.haz_type = 'FL'
impf_flood.set_step_impf([0, 0.2, 0.5, 1.0, 1.5, 2.0],
                        [0, 0.1, 0.25, 0.5, 0.75, 0.9])

# 4.2 Calcul impact
from climada.engine import Impact
impact = Impact()
impact.calc(exposures, impf_set, hazard_flood)

# 4.3 Validation avec données ONPC
observed_damage = 18e9  # 18 milliards FCFA
modeled_damage = impact.aai_agg
accuracy = 1 - abs(modeled_damage - observed_damage) / observed_damage
print(f"Précision modèle: {accuracy:.1%}")
```

#### **Livrables TP1**
1. **Notebook complété** avec tous les calculs
2. **Cartes d'impact** par commune
3. **Rapport de validation** (2 pages)
4. **Recommandations** pour amélioration modèle

### **TP2 : SÉCHERESSE AGRICOLE NORD 2016**

#### **Objectifs Pédagogiques**
1. **Calculer** indices SPI multi-stations
2. **Spatialiser** la sécheresse par krigeage
3. **Modéliser** exposition agricole multi-cultures
4. **Évaluer** impacts économiques sectoriels
5. **Analyser** scénarios d'adaptation

#### **Structure Détaillée (3 heures)**

**Module 1 : Données agro-météorologiques (30 min)**
```python
# 1.1 Import données multi-stations
drought_data = pd.read_csv('data/secheresse_agriculture_2016.csv')
stations = ['Korhogo', 'Ferkessedougou', 'Bouake', 'Bondoukou']

# 1.2 Calcul SPI-3 par station
from climada.util.statistics import spi
for station in stations:
    data_station = drought_data[drought_data.station == station]
    spi_3 = spi(data_station.precip_mm, scale=3)
    drought_data.loc[drought_data.station == station, 'spi_3'] = spi_3

# 1.3 Visualisation évolution temporelle
fig, ax = plt.subplots(figsize=(12, 6))
for station in stations:
    data = drought_data[drought_data.station == station]
    ax.plot(data.date, data.spi_3, label=station, marker='o')
ax.axhline(-1, color='red', linestyle='--', label='Sécheresse modérée')
ax.axhline(-1.5, color='orange', linestyle='--', label='Sécheresse sévère')
```

**Module 2 : Spatialisation sécheresse (45 min)**
```python
# 2.1 Création grille Nord CI
from climada.util.coordinates import get_grid_points
bounds = {'min_lat': 7.5, 'max_lat': 10.5,
          'min_lon': -7.5, 'max_lon': -2.5}
centroids = get_grid_points(**bounds, res_km=10)

# 2.2 Interpolation krigeage
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF

# Préparation données pour krigeage
coords = drought_data[['longitude', 'latitude']].values
spi_values = drought_data['spi_3'].values

# Modèle krigeage
kernel = RBF(length_scale=1.0)
gp = GaussianProcessRegressor(kernel=kernel)
gp.fit(coords, spi_values)

# Prédiction sur grille
spi_grid = gp.predict(centroids[['lat', 'lon']].values)
```

**Module 3 : Exposition agricole (60 min)**
```python
# 3.1 Import données cultures
agri_exposure = pd.read_csv('data/exposition_agricole_nord_ci.csv')
cultures = ['coton', 'riz_pluvial', 'mais']

# 3.2 Calcul valeur par pixel
for culture in cultures:
    agri_exposure[f'value_{culture}'] = (
        agri_exposure[f'surface_{culture}'] *
        agri_exposure[f'rendement_{culture}'] *
        agri_exposure[f'prix_{culture}']
    )

# 3.3 Création exposures par culture
exposures_agri = {}
for culture in cultures:
    exp = Exposures()
    exp.set_from_gdf(agri_exposure, value_col=f'value_{culture}')
    exposures_agri[culture] = exp
```

**Module 4 : Fonctions impact et calculs (45 min)**
```python
# 4.1 Fonctions vulnérabilité par culture
impact_functions = {}

# Coton
impf_coton = ImpactFunc()
impf_coton.haz_type = 'DR'  # DR = Drought
impf_coton.set_step_impf([-3, -2, -1.5, -1, 0], [0.4, 0.25, 0.125, 0, 0])
impact_functions['coton'] = impf_coton

# Riz pluvial
impf_riz = ImpactFunc()
impf_riz.set_step_impf([-3, -2, -1.5, -1, 0], [0.45, 0.3, 0.15, 0, 0])
impact_functions['riz_pluvial'] = impf_riz

# Maïs
impf_mais = ImpactFunc()
impf_mais.set_step_impf([-3, -2, -1.5, -1, 0], [0.3, 0.2, 0.1, 0, 0])
impact_functions['mais'] = impf_mais

# 4.2 Calcul impacts par culture
impacts_by_culture = {}
for culture in cultures:
    impact = Impact()
    impact.calc(exposures_agri[culture],
               impact_functions[culture],
               hazard_drought)
    impacts_by_culture[culture] = impact
```

#### **Livrables TP2**
1. **Cartes SPI** évolution mensuelle 2016
2. **Analyse impact** par culture et région
3. **Scénarios adaptation** (irrigation, variétés résistantes)
4. **Recommandations politiques** sectorielles

### **ÉVALUATION ET CERTIFICATION**

#### **QCM Avant Formation (15 questions)**
**Thèmes couverts** :
- Concepts risques climatiques (5 questions)
- Connaissances Python/SIG (5 questions)
- Contexte Côte d'Ivoire (5 questions)

**Exemple questions** :
1. Qu'est-ce que le SPI ?
2. Quelle est la différence entre aléa et risque ?
3. Citez 3 sources de données météo en CI

#### **QCM Après Formation (20 questions)**
**Thèmes couverts** :
- Maîtrise CLIMADA (8 questions)
- Application cas pratiques (6 questions)
- Interprétation résultats (6 questions)

**Seuil de réussite** : 70% (14/20)

#### **Certification**
- **Attestation DGE** : Formation CLIMADA Côte d'Ivoire
- **Durée validité** : 3 ans
- **Conditions renouvellement** : Formation continue ou projet appliqué

### **ÉLÉMENTS VISUELS**
- **Captures écran** : Notebooks Jupyter
- **Exemples sorties** : Cartes, graphiques, tableaux
- **Planning détaillé** : Chronogramme 6 heures
- **Certificat modèle** : Template attestation

---

## 🚀 **SLIDE 11 : PERSPECTIVES ET APPLICATIONS FUTURES**

### **TITRE**
# Extensions Possibles et Intégration Institutionnelle

### **EXTENSIONS TECHNIQUES ENVISAGEABLES**

#### **Nouveaux Aléas Climatiques**

**Cyclones Tropicaux** :
- **Zone d'intérêt** : Côte atlantique (Abidjan à San-Pédro)
- **Données requises** : Vents, pression, trajectoires
- **Sources** : NOAA, ECMWF, modèles régionaux
- **Applications** : Infrastructures portuaires, zones industrielles

**Élévation Niveau de la Mer** :
- **Horizon temporel** : 2050, 2100
- **Scénarios RCP** : 2.6, 4.5, 8.5
- **Zones critiques** : Lagune Ébrié, Grand-Bassam
- **Impacts** : Érosion côtière, salinisation

**Canicules Urbaines** :
- **Indicateurs** : Température maximale, indice chaleur
- **Zone pilote** : District d'Abidjan
- **Secteurs** : Santé publique, consommation énergétique
- **Données** : Stations météo urbaines, îlots de chaleur

**Feux de Brousse** :
- **Zone d'intérêt** : Régions forestières (Ouest, Centre-Ouest)
- **Indices** : FWI (Fire Weather Index), humidité combustibles
- **Applications** : Forêts classées, plantations industrielles

#### **Secteurs d'Application Étendus**

**Santé Publique** :
- **Maladies vectorielles** : Paludisme, dengue, fièvre jaune
- **Maladies hydriques** : Choléra, diarrhées
- **Stress thermique** : Mortalité liée à la chaleur
- **Partenaires** : INHP, OMS, districts sanitaires

**Énergie** :
- **Hydroélectricité** : Impact sécheresse sur barrages
- **Demande climatisation** : Pics de consommation
- **Énergies renouvelables** : Potentiel solaire/éolien
- **Partenaires** : CI-ENERGIES, ANARE

**Transport** :
- **Infrastructure routière** : Dégradation par inondations
- **Transport fluvial** : Navigabilité en période sèche
- **Aéroports** : Impacts vents forts, visibilité
- **Partenaires** : AGEROUTE, PAA

**Tourisme** :
- **Destinations balnéaires** : Érosion côtière
- **Parcs nationaux** : Accessibilité, biodiversité
- **Événements** : Annulations liées météo
- **Partenaires** : Ministère Tourisme, OIPR

### **INTÉGRATION DANS POLITIQUES PUBLIQUES**

#### **Plan National de Développement (PND 2021-2025)**

**Axe 1 : Transformation Structurelle**
- **Évaluation risques** projets industriels
- **Localisation optimale** infrastructures
- **Résilience** chaînes de valeur agricoles

**Axe 2 : Développement Capital Humain**
- **Planification** infrastructures sanitaires/éducatives
- **Gestion risques** sécurité alimentaire
- **Adaptation** systèmes sociaux

**Axe 3 : Amélioration Cadre de Vie**
- **Aménagement urbain** résilient
- **Gestion** ressources en eau
- **Protection** environnement

#### **Stratégies Sectorielles**

**Agriculture** :
- **Zonage agro-écologique** adapté au climat
- **Assurance agricole** basée sur indices
- **Variétés résistantes** sécheresse/inondations
- **Systèmes d'alerte** précoce

**Aménagement du Territoire** :
- **Cartes risques** pour schémas directeurs
- **Normes construction** en zones inondables
- **Corridors écologiques** adaptation
- **Planification** relocalisations

**Finances Publiques** :
- **Budgétisation** fonds adaptation
- **Évaluation** projets d'investissement
- **Mécanismes** transfert risques
- **Obligations vertes** résilience

### **DÉVELOPPEMENT CAPACITÉS NATIONALES**

#### **Formation Continue**

**Niveau 1 : Sensibilisation** (1 jour)
- **Public** : Décideurs, cadres supérieurs
- **Contenu** : Concepts, enjeux, opportunités
- **Format** : Conférences, démonstrations

**Niveau 2 : Utilisateurs** (3 jours)
- **Public** : Techniciens, analystes
- **Contenu** : Manipulation outils, cas pratiques
- **Format** : Ateliers hands-on

**Niveau 3 : Experts** (5 jours)
- **Public** : Spécialistes, chercheurs
- **Contenu** : Développement modèles, recherche
- **Format** : Formation avancée, projets

#### **Réseau d'Expertise**

**Institutions Partenaires** :
- **Universités** : Félix Houphouët-Boigny, Bouaké, Korhogo
- **Centres recherche** : CNRA, IRD, CIRAD
- **Bureaux d'études** : BNETD, consultants privés
- **Organisations internationales** : Banque Mondiale, BAD, PNUD

**Mécanismes Collaboration** :
- **Groupe de travail** CLIMADA-CI (réunions trimestrielles)
- **Plateforme** partage données et résultats
- **Projets** recherche collaborative
- **Échanges** avec communauté internationale

### **FINANCEMENT ET DURABILITÉ**

#### **Sources de Financement**

**National** :
- **Budget DGE** : Formation, équipement
- **Fonds adaptation** : Projets pilotes
- **Partenariats** public-privé

**International** :
- **Fonds Vert Climat** : Projets adaptation
- **Banque Mondiale** : Renforcement capacités
- **Coopération bilatérale** : Suisse (ETH), France, Allemagne
- **Programmes UE** : Horizon Europe, ACP-UE

#### **Modèle Économique**

**Phase 1 (2024-2025)** : Amorçage
- Financement externe formation initiale
- Développement cas d'usage pilotes
- Constitution équipe nationale

**Phase 2 (2026-2028)** : Consolidation
- Intégration processus institutionnels
- Autofinancement partiel
- Expansion applications sectorielles

**Phase 3 (2029+)** : Autonomie
- Expertise nationale confirmée
- Services payants autres pays
- Centre d'excellence régional

### **ÉLÉMENTS VISUELS**
- **Carte extensions** : Nouveaux aléas et secteurs
- **Chronogramme** : Phases développement 2024-2030
- **Organigramme** : Réseau institutions partenaires
- **Graphiques** : Projections financement et capacités

---

## 📞 **SLIDE 12 : RESSOURCES ET SUPPORT TECHNIQUE**

### **TITRE**
# Ressources Disponibles et Mécanismes de Support

### **DOCUMENTATION COMPLÈTE**

#### **Manuel de Formation (120 pages)**
**Structure** :
1. **Introduction CLIMADA** (20 pages)
   - Concepts fondamentaux
   - Architecture logicielle
   - Installation et configuration

2. **Cas Inondations Abidjan** (40 pages)
   - Méthodologie détaillée
   - Code Python commenté
   - Interprétation résultats

3. **Cas Sécheresse Nord** (35 pages)
   - Calcul indices SPI
   - Modélisation agricole
   - Analyse économique

4. **Extensions et Perspectives** (25 pages)
   - Nouveaux aléas
   - Applications sectorielles
   - Intégration institutionnelle

**Format** : PDF interactif avec liens, annexes techniques

#### **Notebooks Jupyter Commentés**
**TP1_Inondations_Abidjan_2018.ipynb** :
- 15 sections progressives
- 200+ lignes code commenté
- Visualisations intégrées
- Exercices d'approfondissement

**TP2_Secheresse2016_NordCI.ipynb** :
- 12 modules thématiques
- 180+ lignes code commenté
- Analyses statistiques
- Scénarios d'adaptation

**Notebooks_Supplementaires/** :
- Installation et configuration
- Manipulation données géospatiales
- Visualisations avancées
- Bonnes pratiques développement

#### **Datasets Exemples Côte d'Ivoire**
**Données Météorologiques** :
- `port_bouet_juin_2018.csv` : Précipitations horaires
- `stations_nord_2016.csv` : Données multi-stations
- `spi_historique_1981_2020.csv` : Séries SPI longues

**Données Économiques** :
- `exposition_economique_abidjan.csv` : Valeurs par commune
- `exposition_agricole_nord_ci.csv` : Cultures et surfaces
- `fonctions_vulnerabilite_ci.json` : Courbes adaptées

**Données Géospatiales** :
- `communes_abidjan.shp` : Limites administratives
- `regions_nord_ci.shp` : Découpage régional
- `mnt_cote_ivoire_1km.tif` : Modèle numérique terrain

### **SUPPORT TECHNIQUE MULTI-NIVEAUX**

#### **Niveau 1 : Documentation en Ligne**
**Site Officiel CLIMADA** :
- URL : https://climada.readthedocs.io/
- Contenu : Documentation complète, API, tutoriels
- Langues : Anglais (français en développement)
- Mise à jour : Continue

**GitHub Repository** :
- URL : https://github.com/CLIMADA-project/climada_python
- Contenu : Code source, exemples, issues
- Contribution : Communauté open-source
- Licence : GPL v3

#### **Niveau 2 : Communauté Utilisateurs**
**Forum CLIMADA** :
- Plateforme : GitHub Discussions
- Thèmes : Questions techniques, partage expériences
- Modération : Équipe ETH Zurich
- Réactivité : 24-48h

**Réseau Africain** :
- Coordination : ETH Zurich + partenaires régionaux
- Pays participants : Sénégal, Ghana, Kenya, Afrique du Sud
- Activités : Webinaires, ateliers, projets collaboratifs
- Fréquence : Réunions trimestrielles

#### **Niveau 3 : Support Formation DGE**
**Équipe Nationale** :
- **Coordinateur technique** : [Nom, contact]
- **Spécialiste données** : [Nom, contact]
- **Expert SIG** : [Nom, contact]

**Services Proposés** :
- **Hotline technique** : <EMAIL>
- **Sessions de suivi** : Mensuelles (3 premiers mois)
- **Assistance projets** : Sur demande
- **Formation continue** : Modules avancés

### **OUTILS COMPLÉMENTAIRES**

#### **Interfaces Utilisateur**
**CLIMADA Dashboard** (en développement) :
- Interface web intuitive
- Visualisations interactives
- Exports automatisés
- Accès multi-utilisateurs

**Plugin QGIS** :
- Intégration SIG desktop
- Workflows simplifiés
- Cartographie avancée
- Compatible QGIS 3.16+

#### **APIs et Services Web**
**API REST CLIMADA-CI** :
- Endpoints : Calculs impact, données météo
- Authentification : Token-based
- Formats : JSON, GeoJSON
- Documentation : OpenAPI/Swagger

**Services Cloud** :
- **Calcul haute performance** : Instances AWS/Azure
- **Stockage données** : S3, bases géospatiales
- **Notebooks partagés** : JupyterHub
- **Collaboration** : Git, versioning

### **MISE À JOUR ET ÉVOLUTION**

#### **Cycle de Développement**
**Versions CLIMADA** :
- **Majeure** : Annuelle (nouvelles fonctionnalités)
- **Mineure** : Trimestrielle (améliorations, corrections)
- **Patches** : Mensuelle (corrections bugs)

**Adaptations Côte d'Ivoire** :
- **Données locales** : Mise à jour continue
- **Fonctions vulnérabilité** : Calibration annuelle
- **Nouveaux aléas** : Développement selon besoins
- **Formation** : Révision bi-annuelle

#### **Veille Technologique**
**Sources d'Information** :
- **Publications scientifiques** : Climate Risk Management, NHESS
- **Conférences** : EGU, AGU, conférences climat
- **Projets européens** : Horizon Europe, Copernicus
- **Réseaux professionnels** : UNDRR, IPCC

### **CONTACTS ET COORDINATION**

#### **Contacts Principaux**
**Formation et Support** :
- **Coordinateur DGE** : [Nom, email, téléphone]
- **Support technique** : <EMAIL>
- **Partenariats** : <EMAIL>

**Institutions Partenaires** :
- **SODEXAM** : [Contact données météo]
- **INS** : [Contact données économiques]
- **BNETD** : [Contact données géospatiales]
- **Universités** : [Contacts recherche]

#### **Coordination Internationale**
**ETH Zurich** :
- **Équipe CLIMADA** : <EMAIL>
- **Responsable Afrique** : [Nom, contact]
- **Support technique** : GitHub issues

**Réseau Régional** :
- **Coordinateur Afrique Ouest** : [Contact]
- **Partenaires** : ACMAD, AGRHYMET, universités

### **ÉLÉMENTS VISUELS**
- **Captures écran** : Documentation, forums, interfaces
- **Logos** : ETH Zurich, partenaires, certifications
- **QR codes** : Liens rapides ressources en ligne
- **Contacts** : Cartes de visite, organigramme équipe

---

## 🙏 **SLIDE 13 : CONCLUSION ET PROCHAINES ÉTAPES**

### **TITRE**
# Conclusion : Vers une Côte d'Ivoire Résiliente

### **SYNTHÈSE DES BÉNÉFICES ATTENDUS**

#### **Pour la Direction Générale de l'Économie**
**Capacités Techniques Renforcées** :
- **Quantification précise** des risques climatiques (±5% précision démontrée)
- **Outils d'aide à la décision** pour investissements publics
- **Méthodologies standardisées** reconnues internationalement
- **Expertise nationale** en modélisation des risques

**Impact Opérationnel** :
- **Intégration** dans processus budgétaires
- **Support** évaluation projets d'adaptation
- **Amélioration** planification sectorielle
- **Renforcement** crédibilité analyses économiques

#### **Pour les Institutions Partenaires**
**SODEXAM** :
- **Valorisation** patrimoine de données météorologiques
- **Nouveaux services** à valeur ajoutée
- **Renforcement** coopération internationale

**INS** :
- **Enrichissement** comptes économiques régionaux
- **Nouveaux indicateurs** résilience économique
- **Amélioration** statistiques sectorielles

**MINADER** :
- **Outils d'évaluation** risques agricoles
- **Support** développement assurance agricole
- **Amélioration** conseil aux producteurs

#### **Pour la Côte d'Ivoire**
**Positionnement Régional** :
- **Leadership** en modélisation risques climatiques
- **Centre d'excellence** Afrique de l'Ouest
- **Attractivité** pour financements climat
- **Influence** dans négociations internationales

### **PLAN D'ACTION DÉTAILLÉ**

#### **Phase 1 : Formation Intensive (Mois 1-2)**

**Semaine 1 : Formation Initiale**
- **Jour 1** : Concepts et méthodologie
- **Jour 2** : TP1 Inondations Abidjan
- **Jour 3** : TP2 Sécheresse Nord
- **Évaluation** : QCM et certification

**Semaine 2-4 : Approfondissement**
- **Projets individuels** sur données réelles
- **Sessions de suivi** hebdomadaires
- **Développement** cas d'usage spécifiques
- **Constitution** équipe nationale

**Mois 2 : Consolidation**
- **Formation formateurs** (train-the-trainer)
- **Documentation** procédures internes
- **Tests** sur nouveaux cas d'étude
- **Validation** méthodologies adaptées

#### **Phase 2 : Projets Pilotes (Mois 3-8)**

**Projet Pilote 1 : Évaluation Risque Inondation Yamoussoukro**
- **Objectif** : Extension méthodologie à capitale politique
- **Durée** : 3 mois
- **Partenaires** : Mairie, SODEXAM, BNETD
- **Livrables** : Cartes risque, recommandations aménagement

**Projet Pilote 2 : Assurance Agricole Indicielle**
- **Objectif** : Développement produit assurance sécheresse
- **Durée** : 4 mois
- **Partenaires** : MINADER, compagnies d'assurance
- **Livrables** : Indices déclenchement, tarification

**Projet Pilote 3 : Évaluation Économique Adaptation**
- **Objectif** : Analyse coût-bénéfice mesures adaptation
- **Durée** : 3 mois
- **Partenaires** : Ministère Environnement, bailleurs
- **Livrables** : Étude économique, recommandations politiques

#### **Phase 3 : Intégration Institutionnelle (Mois 9-18)**

**Intégration Processus DGE** :
- **Procédures** évaluation projets d'investissement
- **Formation** équipes sectorielles
- **Développement** indicateurs de suivi
- **Reporting** régulier aux autorités

**Extension Sectorielle** :
- **Santé** : Modélisation maladies vectorielles
- **Énergie** : Impact sur production hydroélectrique
- **Transport** : Vulnérabilité infrastructures
- **Tourisme** : Évaluation destinations côtières

**Coopération Régionale** :
- **Partage expériences** avec pays voisins
- **Formation** experts régionaux
- **Projets** transfrontaliers (bassins versants)
- **Harmonisation** méthodologies sous-régionales

### **INDICATEURS DE SUCCÈS**

#### **Indicateurs Quantitatifs**
**Technique** :
- **Nombre** de modèles développés : 5+ par an
- **Précision** des prédictions : >90%
- **Couverture** géographique : 100% territoire national
- **Fréquence** mise à jour : Trimestrielle

**Institutionnel** :
- **Nombre** de projets intégrant CLIMADA : 10+ par an
- **Personnel formé** : 25+ techniciens
- **Partenariats** actifs : 8+ institutions
- **Publications** scientifiques : 3+ par an

**Économique** :
- **Montant** projets évalués : 500+ milliards FCFA/an
- **Économies** réalisées : 5% coûts adaptation
- **Financements** mobilisés : 50+ milliards FCFA
- **ROI formation** : 1:10 (bénéfices/coûts)

#### **Indicateurs Qualitatifs**
**Reconnaissance** :
- **Adoption** par institutions nationales
- **Référencement** dans politiques publiques
- **Reconnaissance** communauté internationale
- **Prix/distinctions** pour innovation

### **VISION 2030**

#### **Côte d'Ivoire, Hub Régional d'Excellence**
> *"Faire de la Côte d'Ivoire le centre de référence en Afrique de l'Ouest pour la modélisation des risques climatiques et l'évaluation économique de l'adaptation"*

**Objectifs Stratégiques** :
1. **100% des projets** d'investissement public intègrent évaluation risques climatiques
2. **Centre de formation** régional CLIMADA opérationnel
3. **Réseau** de 15 pays africains utilisateurs
4. **Contribution** significative à la recherche internationale

**Impact Attendu** :
- **Réduction** 30% des pertes économiques liées au climat
- **Amélioration** 50% efficacité investissements adaptation
- **Création** 100+ emplois spécialisés
- **Positionnement** leader négociations climat

### **APPEL À L'ACTION**

#### **Engagement Immédiat**
1. **Validation** plan de formation par hiérarchie DGE
2. **Désignation** équipe projet (5-8 personnes)
3. **Allocation** budget formation et équipement
4. **Signature** accords partenariat institutions

#### **Soutien Requis**
- **Direction DGE** : Portage politique et budgétaire
- **Partenaires techniques** : Mise à disposition données
- **Coopération internationale** : Financement et expertise
- **Universités** : Recherche et formation continue

### **MESSAGE FINAL**
> *"CLIMADA n'est pas seulement un outil technique, c'est un levier de transformation pour une Côte d'Ivoire plus résiliente face aux défis climatiques. Ensemble, construisons l'expertise nationale qui permettra à notre pays de s'adapter et de prospérer dans un climat changeant."*

### **ÉLÉMENTS VISUELS**
- **Chronogramme** : Planning 18 mois détaillé
- **Carte vision** : CI hub régional 2030
- **Graphiques** : Indicateurs de succès et projections
- **Photos** : Équipes, formations, événements

---

## 🎤 **SLIDE 14 : QUESTIONS ET DISCUSSIONS**

### **TITRE**
# Questions et Discussions

### **THÈMES DE DISCUSSION SUGGÉRÉS**

#### **Questions Techniques**
- Adaptation de CLIMADA aux spécificités locales
- Intégration avec systèmes d'information existants
- Besoins en formation complémentaire
- Défis techniques anticipés

#### **Questions Institutionnelles**
- Modalités de collaboration inter-institutionnelle
- Mécanismes de financement durable
- Intégration dans processus décisionnels
- Gouvernance du projet

#### **Questions Stratégiques**
- Positionnement régional et international
- Opportunités de valorisation
- Synergies avec autres initiatives
- Vision à long terme

### **CONTACTS POUR SUIVI**

**Coordination Générale** :
- Email : <EMAIL>
- Téléphone : [Numéro dédié]

**Support Technique** :
- Email : <EMAIL>
- Plateforme : GitHub CLIMADA-CI

### **PROCHAINES ÉTAPES IMMÉDIATES**
1. **Recueil feedback** participants
2. **Planification** sessions de formation
3. **Constitution** équipe projet
4. **Lancement** phase pilote

---

**Merci pour votre attention et votre engagement !**

*Ensemble, construisons une Côte d'Ivoire résiliente face aux défis climatiques.*
