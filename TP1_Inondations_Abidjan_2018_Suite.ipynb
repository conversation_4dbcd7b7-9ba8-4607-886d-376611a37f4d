{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Module 4 : Fonctions de Vulnérabilité et Calcul d'Impact (30 min)\n", "\n", "### 4.1 Définition des fonctions de vulnérabilité"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import des classes CLIMADA pour les fonctions d'impact\n", "from climada.entity import ImpactFunc, ImpactFuncSet\n", "\n", "def create_flood_impact_functions():\n", "    \"\"\"\n", "    Crée les fonctions de vulnérabilité pour différents types de bâtiments\n", "    adaptées au contexte ivoirien\n", "    \"\"\"\n", "    impact_funcs = ImpactFuncSet()\n", "    \n", "    # Fonction 1: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    impf_residential = ImpactFunc()\n", "    impf_residential.haz_type = 'FL'  # Flood\n", "    impf_residential.id = 1\n", "    impf_residential.name = 'Résidentiel Abidjan'\n", "    \n", "    # Courbe hauteur d'eau (mm) vs pourcentage de dommage\n", "    # Adaptée aux constructions ivoiriennes (souvent surélevées)\n", "    intensities_res = [0, 200, 500, 1000, 1500, 2000, 3000]  # mm\n", "    damages_res = [0, 0.05, 0.15, 0.35, 0.60, 0.80, 0.90]   # fraction\n", "    \n", "    impf_residential.set_step_impf(intensities_res, damages_res)\n", "    \n", "    # Fonction 2: Commercial\n", "    impf_commercial = ImpactFunc()\n", "    impf_commercial.haz_type = 'FL'\n", "    impf_commercial.id = 2\n", "    impf_commercial.name = 'Commercial Abidjan'\n", "    \n", "    # Plus sensible (stocks, équipements au sol)\n", "    intensities_com = [0, 100, 300, 800, 1500, 2500]  # mm\n", "    damages_com = [0, 0.10, 0.25, 0.50, 0.75, 0.95]  # fraction\n", "    \n", "    impf_commercial.set_step_impf(intensities_com, damages_com)\n", "    \n", "    # Fonction 3: Industriel\n", "    impf_industrial = ImpactFunc()\n", "    impf_industrial.haz_type = 'FL'\n", "    impf_industrial.id = 3\n", "    impf_industrial.name = 'Industriel Abidjan'\n", "    \n", "    # Très sensible (machines, processus)\n", "    intensities_ind = [0, 50, 200, 500, 1000, 2000]  # mm\n", "    damages_ind = [0, 0.15, 0.35, 0.60, 0.85, 0.95]  # fraction\n", "    \n", "    impf_industrial.set_step_impf(intensities_ind, damages_ind)\n", "    \n", "    # Ajout à l'ensemble\n", "    impact_funcs.append(impf_residential)\n", "    impact_funcs.append(impf_commercial)\n", "    impact_funcs.append(impf_industrial)\n", "    \n", "    return impact_funcs\n", "\n", "# Création des fonctions d'impact\n", "impact_functions = create_flood_impact_functions()\n", "\n", "print(\"📈 FONCTIONS DE VULNÉRABILITÉ CRÉÉES:\")\n", "for impf in impact_functions:\n", "    print(f\"  ID {impf.id}: {impf.name}\")\n", "    print(f\"    Intensité max: {max(impf.intensity):.0f} mm\")\n", "    print(f\"    Dommage max: {max(impf.mdd)*100:.0f}%\")\n", "\n", "# Vérification\n", "impact_functions.check()\n", "print(\"\\n✅ Fonctions de vulnérabilité validées!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Visualisation des courbes de vulnérabilité"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Graphique des courbes de vulnérabilité\n", "plt.figure(figsize=(12, 8))\n", "\n", "colors = ['blue', 'green', 'red']\n", "markers = ['o', 's', '^']\n", "\n", "for i, impf in enumerate(impact_functions):\n", "    plt.plot(impf.intensity, impf.mdd * 100, \n", "             color=colors[i], marker=markers[i], linewidth=2, markersize=8,\n", "             label=impf.name)\n", "\n", "plt.xlabel('Hauteur d\\'eau (mm)', fontsize=12)\n", "plt.ylabel('Pourcentage de dommage (%)', fontsize=12)\n", "plt.title('Courbes de Vulnérabilité - Inondations Abidjan\\n(Adaptées au contexte ivoirien)', \n", "          fontsize=14, fontweight='bold')\n", "plt.grid(True, alpha=0.3)\n", "plt.legend(fontsize=11)\n", "plt.xlim(0, 3000)\n", "plt.ylim(0, 100)\n", "\n", "# Ajout de lignes de référence\n", "plt.axhline(50, color='orange', linestyle='--', alpha=0.7, label='50% dommage')\n", "plt.axvline(1000, color='purple', linestyle='--', alpha=0.7, label='1m hauteur')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Tableau comparatif pour hauteurs d'eau typiques\n", "test_heights = [100, 300, 500, 1000, 1500, 2000]  # mm\n", "\n", "print(\"\\n📊 DOMMAGES PAR TYPE DE BÂTIMENT (% de la valeur):\")\n", "print(\"=\"*70)\n", "print(f\"{'Hauteur (mm)':<12} {'Résidentiel':<12} {'Commercial':<12} {'Industriel':<12}\")\n", "print(\"=\"*70)\n", "\n", "for height in test_heights:\n", "    damages = []\n", "    for impf in impact_functions:\n", "        # Interpolation pour obtenir le dommage à cette hauteur\n", "        damage_pct = np.interp(height, impf.intensity, impf.mdd) * 100\n", "        damages.append(damage_pct)\n", "    \n", "    print(f\"{height:<12} {damages[0]:<12.1f} {damages[1]:<12.1f} {damages[2]:<12.1f}\")\n", "\n", "print(\"\\n💡 INTERPRÉTATION:\")\n", "print(\"- Résidentiel: Plus résistant (constructions souvent surélevées)\")\n", "print(\"- Commercial: Sensibilité moyenne (stocks et équipements)\")\n", "print(\"- Industriel: <PERSON>r<PERSON> sensible (machines et processus critiques)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Attribution des fonctions de vulnérabilité aux expositions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapping secteur -> ID fonction de vulnérabilité\n", "secteur_to_impf = {\n", "    'residentiel': 1,\n", "    'commercial': 2,\n", "    'industriel': 3,\n", "    'services': 2,      # Traité comme commercial\n", "    'public': 1,        # Traité comme résidentiel\n", "    'transport': 3      # Traité comme industriel\n", "}\n", "\n", "# Attribution des IDs de fonction d'impact\n", "exposures.gdf['impf_FL'] = exposures.gdf['secteur'].map(secteur_to_impf)\n", "\n", "# Vérification de l'attribution\n", "print(\"🔗 ATTRIBUTION FONCTIONS DE VULNÉRABILITÉ:\")\n", "attribution_stats = exposures.gdf.groupby(['secteur', 'impf_FL']).size().reset_index(name='count')\n", "for _, row in attribution_stats.iterrows():\n", "    impf_name = impact_functions.get_func('FL', row['impf_FL'])[0].name\n", "    print(f\"  {row['secteur']} → ID {row['impf_FL']} ({impf_name}): {row['count']} actifs\")\n", "\n", "# Vérification qu'aucun actif n'a d'ID manquant\n", "missing_impf = exposures.gdf['impf_FL'].isna().sum()\n", "if missing_impf > 0:\n", "    print(f\"⚠️ ATTENTION: {missing_impf} actifs sans fonction de vulnérabilité!\")\n", "else:\n", "    print(\"✅ Tous les actifs ont une fonction de vulnérabilité assignée\")\n", "\n", "# Mise à jour de l'objet exposures\n", "exposures.check()\n", "print(\"\\n✅ Exposures mises à jour avec succès!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.4 Calcul de l'impact économique"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul de l'impact avec CLIMADA\n", "from climada.engine import Impact\n", "\n", "# Création de l'objet Impact\n", "impact = Impact()\n", "\n", "# Calcul des dommages\n", "print(\"⚙️ Calcul de l'impact en cours...\")\n", "impact.calc(exposures, impact_functions, hazard_flood)\n", "\n", "# Résultats globaux\n", "total_damage = impact.aai_agg  # Average Annual Impact aggregated\n", "event_damage = impact.at_event[0]  # Dommage pour l'événement unique\n", "\n", "print(\"\\n💥 RÉSULTATS IMPACT ÉCONOMIQUE:\")\n", "print(\"=\"*50)\n", "print(f\"Dommages totaux: {event_damage/1e9:.2f} milliards FCFA\")\n", "print(f\"Dommages annuels moyens: {total_damage/1e9:.2f} milliards FCFA\")\n", "print(f\"Fréquence événement: {hazard_flood.frequency[0]:.6f} /an\")\n", "print(f\"Période de retour: {1/hazard_flood.frequency[0]:.0f} ans\")\n", "\n", "# Comparaison avec dommages observés ONPC\n", "observed_damage = 18e9  # 18 milliards FCFA selon ONPC\n", "accuracy = (1 - abs(event_damage - observed_damage) / observed_damage) * 100\n", "bias = ((event_damage - observed_damage) / observed_damage) * 100\n", "\n", "print(f\"\\n🎯 VALIDATION AVEC DONNÉES ONPC:\")\n", "print(f\"Dommages observés (ONPC): {observed_damage/1e9:.1f} milliards FCFA\")\n", "print(f\"Dommages modélisés: {event_damage/1e9:.1f} milliards FCFA\")\n", "print(f\"Précision: {accuracy:.1f}%\")\n", "print(f\"Biais: {bias:+.1f}% ({'surestimation' if bias > 0 else 'sous-estimation'})\")\n", "\n", "if accuracy >= 90:\n", "    print(\"✅ EXCELLENTE précision du modèle!\")\n", "elif accuracy >= 80:\n", "    print(\"✅ BONNE précision du modèle\")\n", "else:\n", "    print(\"⚠️ Précision à améliorer\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> 5 : <PERSON><PERSON><PERSON> Résultats (30 min)\n", "\n", "### 5.1 Analyse spatiale des dommages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extraction des dommages par actif\n", "exposures.gdf['damage_fcfa'] = impact.imp_mat[0, :] * exposures.gdf['value']\n", "exposures.gdf['damage_percent'] = (exposures.gdf['damage_fcfa'] / exposures.gdf['value']) * 100\n", "\n", "# Analyse par commune\n", "commune_damage = exposures.gdf.groupby('commune').agg({\n", "    'damage_fcfa': 'sum',\n", "    'value': 'sum',\n", "    'damage_percent': 'mean'\n", "}).round(2)\n", "\n", "commune_damage['damage_milliards'] = commune_damage['damage_fcfa'] / 1e9\n", "commune_damage['exposition_milliards'] = commune_damage['value'] / 1e9\n", "commune_damage['taux_dommage'] = (commune_damage['damage_fcfa'] / commune_damage['value']) * 100\n", "\n", "# Tri par dommages décroissants\n", "commune_damage = commune_damage.sort_values('damage_fcfa', ascending=False)\n", "\n", "print(\"🏙️ DOMMAGES PAR COMMUNE:\")\n", "print(\"=\"*90)\n", "print(f\"{'Commune':<15} {'Dommages (Mds)':<15} {'Exposition (Mds)':<16} {'Taux (%)':<10}\")\n", "print(\"=\"*90)\n", "\n", "for commune, row in commune_damage.iterrows():\n", "    print(f\"{commune:<15} {row['damage_milliards']:<15.2f} {row['exposition_milliards']:<16.1f} {row['taux_dommage']:<10.1f}\")\n", "\n", "# Analyse par secteur\n", "secteur_damage = exposures.gdf.groupby('secteur').agg({\n", "    'damage_fcfa': 'sum',\n", "    'value': 'sum'\n", "})\n", "\n", "secteur_damage['damage_milliards'] = secteur_damage['damage_fcfa'] / 1e9\n", "secteur_damage['taux_dommage'] = (secteur_damage['damage_fcfa'] / secteur_damage['value']) * 100\n", "secteur_damage = secteur_damage.sort_values('damage_fcfa', ascending=False)\n", "\n", "print(f\"\\n🏢 DOMMAGES PAR SECTEUR:\")\n", "print(\"=\"*70)\n", "print(f\"{'Secteur':<15} {'Dommages (Mds)':<15} {'Taux (%)':<10}\")\n", "print(\"=\"*70)\n", "\n", "for secteur, row in secteur_damage.iterrows():\n", "    print(f\"{secteur:<15} {row['damage_milliards']:<15.2f} {row['taux_dommage']:<10.1f}\")\n", "\n", "# Statistiques générales\n", "print(f\"\\n📊 STATISTIQUES GÉNÉRALES:\")\n", "print(f\"Nombre d'actifs endommagés: {(exposures.gdf['damage_fcfa'] > 0).sum():,}\")\n", "print(f\"Pourcentage d'actifs endommagés: {(exposures.gdf['damage_fcfa'] > 0).mean()*100:.1f}%\")\n", "print(f\"Dommage moyen par actif touché: {exposures.gdf[exposures.gdf['damage_fcfa'] > 0]['damage_fcfa'].mean()/1e6:.1f} millions FCFA\")\n", "print(f\"Dommage maximum: {exposures.gdf['damage_fcfa'].max()/1e6:.1f} millions FCFA\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Visualisations des résultats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création de visualisations complètes\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Graphique 1: Dommages par commune\n", "ax1 = axes[0, 0]\n", "commune_damage['damage_milliards'].plot(kind='bar', ax=ax1, color='coral')\n", "ax1.set_title('Dommages par Commune\\n(milliards FCFA)', fontweight='bold')\n", "ax1.set_ylabel('Dommages (milliards FCFA)')\n", "ax1.tick_params(axis='x', rotation=45)\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Graphique 2: Taux de dommage par commune\n", "ax2 = axes[0, 1]\n", "commune_damage['taux_dommage'].plot(kind='bar', ax=ax2, color='lightblue')\n", "ax2.set_title('Taux de Dommage par Commune\\n(% de la valeur exposée)', fontweight='bold')\n", "ax2.set_ylabel('Taux de dommage (%)')\n", "ax2.tick_params(axis='x', rotation=45)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Graphique 3: Dommages par secteur\n", "ax3 = axes[1, 0]\n", "secteur_damage['damage_milliards'].plot(kind='pie', ax=ax3, autopct='%1.1f%%')\n", "ax3.set_title('Répartition Dommages par Secteur', fontweight='bold')\n", "ax3.set_ylabel('')\n", "\n", "# Graphique 4: Distribution spatiale des dommages\n", "ax4 = axes[1, 1]\n", "scatter = ax4.scatter(exposures.gdf['longitude'], exposures.gdf['latitude'],\n", "                     c=exposures.gdf['damage_fcfa']/1e6, cmap='Reds', \n", "                     s=30, alpha=0.7)\n", "ax4.set_xlabel('Longitude')\n", "ax4.set_ylabel('Latitude')\n", "ax4.set_title('Distribution Spatiale des Dommages', fontweight='bold')\n", "cbar = plt.colorbar(scatter, ax=ax4)\n", "cbar.set_label('Dommages (millions FCFA)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Sauvegarde des résultats détaillés\n", "commune_damage.to_csv('results/dommages_par_commune.csv')\n", "secteur_damage.to_csv('results/dommages_par_secteur.csv')\n", "exposures.gdf[['commune', 'secteur', 'value', 'damage_fcfa', 'damage_percent']].to_csv('results/dommages_detailles.csv')\n", "\n", "print(\"\\n💾 RÉSULTATS SAUVEGARDÉS:\")\n", "print(\"  - results/dommages_par_commune.csv\")\n", "print(\"  - results/dommages_par_secteur.csv\")\n", "print(\"  - results/dommages_detailles.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Rapport de synthèse et recommandations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Génération du rapport de synthèse\n", "rapport = f\"\"\"\n", "RAPPORT DE SYNTHÈSE - MODÉLISATION INONDATIONS ABIDJAN JUIN 2018\n", "================================================================\n", "\n", "1. ÉVÉNEMENT ANALYSÉ\n", "   - Date: 19-20 juin 2018\n", "   - Précipitations maximales: {max_24h:.1f} mm/24h (Port-Bouët)\n", "   - Période de retour estimée: {return_period_event:.0f} ans\n", "\n", "2. RÉSULTATS MODÉLISATION\n", "   - Dommages modélisés: {event_damage/1e9:.2f} milliards FCFA\n", "   - Dommages observés (ONPC): {observed_damage/1e9:.1f} milliards FCFA\n", "   - Précision du modèle: {accuracy:.1f}%\n", "   - Biais: {bias:+.1f}%\n", "\n", "3. RÉPARTITION DES DOMMAGES\n", "   Communes les plus affectées:\n", "\"\"\"\n", "\n", "# Ajout des 3 communes les plus affectées\n", "for i, (commune, row) in enumerate(commune_damage.head(3).iterrows()):\n", "    rapport += f\"   {i+1}. {commune}: {row['damage_milliards']:.2f} milliards FCFA ({row['taux_dommage']:.1f}%)\\n\"\n", "\n", "rapport += f\"\"\"\n", "   Secteurs les plus affectés:\n", "\"\"\"\n", "\n", "for i, (secteur, row) in enumerate(secteur_damage.head(3).iterrows()):\n", "    rapport += f\"   {i+1}. {secteur}: {row['damage_milliards']:.2f} milliards FCFA ({row['taux_dommage']:.1f}%)\\n\"\n", "\n", "rapport += f\"\"\"\n", "4. RECOMMANDATIONS\n", "   - Renforcement drainage dans les communes les plus vulnérables\n", "   - Amélioration des normes de construction en zones inondables\n", "   - Développement système d'alerte précoce\n", "   - Révision des plans d'aménagement urbain\n", "\n", "5. LIMITES ET AMÉLIORATIONS\n", "   - Intégrer données topographiques haute résolution\n", "   - Affiner les fonctions de vulnérabilité par enquêtes terrain\n", "   - Considérer les mesures de protection existantes\n", "   - Analyser les effets de cascade (transport, services)\n", "\n", "Date du rapport: {datetime.now().strftime('%d/%m/%Y %H:%M')}\n", "Générateur: CLIMADA Formation DGE\n", "\"\"\"\n", "\n", "print(rapport)\n", "\n", "# Sauvegarde du rapport\n", "with open('results/rapport_synthese_inondations_abidjan.txt', 'w', encoding='utf-8') as f:\n", "    f.write(rapport)\n", "\n", "print(\"\\n💾 Rapport sauvegardé: results/rapport_synthese_inondations_abidjan.txt\")\n", "\n", "# Résumé des fichiers générés\n", "print(\"\\n📁 FICHIERS GÉNÉRÉS PENDANT CE TP:\")\n", "print(\"  📊 Données:\")\n", "print(\"    - exposition_par_commune.csv\")\n", "    - dommages_par_commune.csv\")\n", "print(\"    - dommages_par_secteur.csv\")\n", "print(\"    - dommages_detailles.csv\")\n", "print(\"  🗺️ Cartes:\")\n", "print(\"    - carte_aleas_inondation_abidjan.html\")\n", "print(\"  📄 Rapports:\")\n", "print(\"    - rapport_synthese_inondations_abidjan.txt\")\n", "\n", "print(\"\\n🎯 TP1 TERMINÉ AVEC SUCCÈS!\")\n", "print(\"Vous maî<PERSON><PERSON>z maintenant le workflow complet CLIMADA pour les inondations urbaines.\")"]}], "metadata": {"kernelspec": {"display_name": "climada", "language": "python", "name": "climada"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}