{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Module 2 : Spatialisation de la Sécheresse par <PERSON>rigeage (45 min)\n", "\n", "### 2.1 Préparation des données pour interpolation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sélection des données pour juillet 2016 (pic de sécher<PERSON>e)\n", "target_date = '2016-07-01'\n", "spi_july_2016 = drought_data_spi[\n", "    drought_data_spi['date'] == target_date\n", "].copy()\n", "\n", "print(f\"📅 DONNÉES POUR SPATIALISATION - {target_date}:\")\n", "print(\"=\"*60)\n", "print(f\"{'Station':<15} {'Latitude':<10} {'Longitude':<11} {'SPI-3':<8}\")\n", "print(\"=\"*60)\n", "\n", "for _, row in spi_july_2016.iterrows():\n", "    print(f\"{row['station']:<15} {row['latitude']:<10.3f} {row['longitude']:<11.3f} {row['spi_3']:<8.2f}\")\n", "\n", "# Vérification des données manquantes\n", "missing_spi = spi_july_2016['spi_3'].isna().sum()\n", "if missing_spi > 0:\n", "    print(f\"\\n⚠️ ATTENTION: {missing_spi} stations avec SPI manquant\")\n", "    spi_july_2016 = spi_july_2016.dropna(subset=['spi_3'])\n", "    print(f\"Stations utilisables: {len(spi_july_2016)}\")\n", "else:\n", "    print(f\"\\n✅ Toutes les stations ont des valeurs SPI valides\")\n", "\n", "# Préparation des coordonnées pour krigeage\n", "coords = spi_july_2016[['longitude', 'latitude']].values\n", "spi_values = spi_july_2016['spi_3'].values\n", "\n", "print(f\"\\n📊 STATISTIQUES SPI JUILLET 2016:\")\n", "print(f\"Minimum: {spi_values.min():.2f}\")\n", "print(f\"Maximum: {spi_values.max():.2f}\")\n", "print(f\"Moyenne: {spi_values.mean():.2f}\")\n", "print(f\"Écart-type: {spi_values.std():.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Création de la grille d'interpolation Nord CI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Définition des limites géographiques du Nord CI\n", "north_ci_bounds = {\n", "    'min_lat': 7.5,    # Sud (limite zone soudanienne)\n", "    'max_lat': 10.5,   # Nord (frontière Mali/Burkina)\n", "    'min_lon': -7.5,   # <PERSON><PERSON> (frontière Guinée/Mali)\n", "    'max_lon': -2.5    # Est (frontière Ghana/Burkina)\n", "}\n", "\n", "# Création grille d'interpolation (résolution 10km)\n", "resolution_km = 10\n", "centroids_north = get_grid_points(**north_ci_bounds, res_km=resolution_km)\n", "\n", "print(f\"🗺️ GRILLE D'INTERPOLATION NORD CI:\")\n", "print(f\"Résolution: {resolution_km} km x {resolution_km} km\")\n", "print(f\"Nombre de points: {len(centroids_north):,}\")\n", "print(f\"Étendue latitude: {north_ci_bounds['min_lat']:.1f}° à {north_ci_bounds['max_lat']:.1f}°N\")\n", "print(f\"Étendue longitude: {north_ci_bounds['min_lon']:.1f}° à {north_ci_bounds['max_lon']:.1f}°W\")\n", "\n", "# Calcul de la superficie couverte\n", "lat_range = north_ci_bounds['max_lat'] - north_ci_bounds['min_lat']\n", "lon_range = north_ci_bounds['max_lon'] - north_ci_bounds['min_lon']\n", "area_deg2 = lat_range * lon_range\n", "area_km2 = area_deg2 * 111 * 111  # Approximation 1° ≈ 111 km\n", "\n", "print(f\"Superficie approximative: {area_km2:,.0f} km²\")\n", "\n", "# Visualisation des stations et de la grille\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Points de grille (échantillonnage pour lisibilité)\n", "sample_grid = centroids_north.iloc[::20]  # 1 point sur 20\n", "plt.scatter(sample_grid['lon'], sample_grid['lat'], \n", "           c='lightgray', s=1, alpha=0.5, label='Grille interpolation')\n", "\n", "# Stations météo avec valeurs SPI\n", "scatter = plt.scatter(spi_july_2016['longitude'], spi_july_2016['latitude'],\n", "                     c=spi_july_2016['spi_3'], cmap='RdYlBu', \n", "                     s=200, edgecolors='black', linewidth=2,\n", "                     vmin=-2.5, vmax=0.5, label='Stations météo')\n", "\n", "# Annotations des stations\n", "for _, row in spi_july_2016.iterrows():\n", "    plt.annotate(f\"{row['station']}\\n({row['spi_3']:.1f})\", \n", "                xy=(row['longitude'], row['latitude']),\n", "                xytext=(5, 5), textcoords='offset points',\n", "                fontsize=9, fontweight='bold',\n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))\n", "\n", "plt.colorbar(scatter, label='SPI-3 Juillet 2016')\n", "plt.xlabel('Longitude')\n", "plt.ylabel('Latitude')\n", "plt.title('Stations Météo et Grille d\\'Interpolation - Nord Côte d\\'Ivoire', fontweight='bold')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Interpolation par krigeage gaussien"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration du modèle de krigeage\n", "from sklearn.gaussian_process import GaussianProcessRegressor\n", "from sklearn.gaussian_process.kernels import RBF, <PERSON><PERSON>, WhiteKernel\n", "from sklearn.model_selection import cross_val_score\n", "\n", "# Test de différents kernels\n", "kernels = {\n", "    'RBF': RBF(length_scale=1.0, length_scale_bounds=(0.1, 10.0)),\n", "    'Matern_1.5': <PERSON><PERSON>(length_scale=1.0, nu=1.5),\n", "    'Matern_2.5': <PERSON><PERSON>(length_scale=1.0, nu=2.5),\n", "    'RBF_Noise': RBF(length_scale=1.0) + WhiteKernel(noise_level=0.1)\n", "}\n", "\n", "print(\"🔍 SÉLECTION DU MEILLEUR KERNEL (validation croisée):\")\n", "print(\"=\"*60)\n", "print(f\"{'Kernel':<15} {'Score CV':<12} {'Std':<8}\")\n", "print(\"=\"*60)\n", "\n", "best_score = -np.inf\n", "best_kernel = None\n", "best_gp = None\n", "\n", "for kernel_name, kernel in kernels.items():\n", "    # <PERSON><PERSON><PERSON><PERSON> de krigeage\n", "    gp = GaussianProcessRegressor(kernel=kernel, \n", "                                 alpha=1e-6,  # Régularisation\n", "                                 normalize_y=True,\n", "                                 random_state=42)\n", "    \n", "    # Validation croisée (Leave-One-Out pour petit échantillon)\n", "    cv_scores = cross_val_score(gp, coords, spi_values, \n", "                               cv=len(coords), scoring='neg_mean_squared_error')\n", "    \n", "    mean_score = cv_scores.mean()\n", "    std_score = cv_scores.std()\n", "    \n", "    print(f\"{kernel_name:<15} {mean_score:<12.3f} {std_score:<8.3f}\")\n", "    \n", "    if mean_score > best_score:\n", "        best_score = mean_score\n", "        best_kernel = kernel_name\n", "        best_gp = gp\n", "\n", "print(f\"\\n🏆 MEILLEUR KERNEL: {best_kernel} (Score: {best_score:.3f})\")\n", "\n", "# Ajuste<PERSON> du modèle final\n", "print(\"\\n⚙️ Ajustement du modèle de krigeage...\")\n", "best_gp.fit(coords, spi_values)\n", "\n", "# Prédiction sur la grille\n", "grid_coords = centroids_north[['lon', 'lat']].values\n", "spi_pred, spi_std = best_gp.predict(grid_coords, return_std=True)\n", "\n", "# Ajout des résultats à la grille\n", "centroids_north['spi_3_pred'] = spi_pred\n", "centroids_north['spi_3_std'] = spi_std\n", "\n", "print(f\"✅ Interpolation terminée!\")\n", "print(f\"SPI prédit - Min: {spi_pred.min():.2f}, Max: {spi_pred.max():.2f}, <PERSON><PERSON>nne: {spi_pred.mean():.2f}\")\n", "print(f\"Incertitude - Min: {spi_std.min():.2f}, Max: {spi_std.max():.2f}, Moyenne: {spi_std.mean():.2f}\")\n", "\n", "# Classification de la sécheresse sur la grille\n", "centroids_north['drought_category'] = centroids_north['spi_3_pred'].apply(classify_drought)\n", "\n", "# Statistiques par catégorie\n", "print(f\"\\n📊 RÉPARTITION DE LA SÉCHERESSE (% de la zone):\")\n", "drought_stats = centroids_north['drought_category'].value_counts(normalize=True) * 100\n", "for category, percent in drought_stats.items():\n", "    print(f\"  {category}: {percent:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 Visualisation de la spatialisation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création de cartes de la sécheresse spatialisée\n", "fig, axes = plt.subplots(1, 2, figsize=(18, 8))\n", "\n", "# Carte 1: SPI interpolé\n", "ax1 = axes[0]\n", "scatter1 = ax1.scatter(centroids_north['lon'], centroids_north['lat'],\n", "                      c=centroids_north['spi_3_pred'], cmap='RdYlBu',\n", "                      s=15, alpha=0.8, vmin=-2.5, vmax=0.5)\n", "\n", "# Superposition des stations\n", "ax1.scatter(spi_july_2016['longitude'], spi_july_2016['latitude'],\n", "           c=spi_july_2016['spi_3'], cmap='RdYlBu',\n", "           s=300, edgecolors='black', linewidth=3,\n", "           vmin=-2.5, vmax=0.5, marker='s')\n", "\n", "# Annotations stations\n", "for _, row in spi_july_2016.iterrows():\n", "    ax1.annotate(row['station'], \n", "                xy=(row['longitude'], row['latitude']),\n", "                xytext=(5, 5), textcoords='offset points',\n", "                fontsize=10, fontweight='bold',\n", "                bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.9))\n", "\n", "cbar1 = plt.colorbar(scatter1, ax=ax1)\n", "cbar1.set_label('SPI-3 Interpolé', fontsize=12)\n", "ax1.set_xlabel('Longitude')\n", "ax1.set_ylabel('Latitude')\n", "ax1.set_title('SPI-3 Spatialisé - Juillet 2016\\n(Carrés = Stations, Points = Interpolation)', fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Carte 2: Incertitude de l'interpolation\n", "ax2 = axes[1]\n", "scatter2 = ax2.scatter(centroids_north['lon'], centroids_north['lat'],\n", "                      c=centroids_north['spi_3_std'], cmap='Reds',\n", "                      s=15, alpha=0.8)\n", "\n", "# Stations (incertitude nulle)\n", "ax2.scatter(spi_july_2016['longitude'], spi_july_2016['latitude'],\n", "           c='blue', s=300, edgecolors='black', linewidth=3, marker='s')\n", "\n", "cbar2 = plt.colorbar(scatter2, ax=ax2)\n", "cbar2.set_label('Écart-type SPI', fontsize=12)\n", "ax2.set_xlabel('Longitude')\n", "ax2.set_ylabel('Latitude')\n", "ax2.set_title('Incertitude de l\\'Interpolation\\n(Bleu = Stations observées)', fontweight='bold')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Carte interactive avec Folium\n", "def create_drought_map(centroids_df, stations_df):\n", "    \"\"\"Crée une carte interactive de la sécheresse\"\"\"\n", "    # Centre de la carte\n", "    center_lat = centroids_df['lat'].mean()\n", "    center_lon = centroids_df['lon'].mean()\n", "    \n", "    # Carte de base\n", "    m = folium.Map(location=[center_lat, center_lon], \n", "                   zoom_start=8, tiles='OpenStreetMap')\n", "    \n", "    # Échantillonnage pour performance\n", "    sample_data = centroids_df.iloc[::5].copy()\n", "    \n", "    # Couleurs selon SPI\n", "    def get_color(spi_val):\n", "        if spi_val >= 0:\n", "            return 'blue'\n", "        elif spi_val >= -1:\n", "            return 'yellow'\n", "        elif spi_val >= -1.5:\n", "            return 'orange'\n", "        elif spi_val >= -2:\n", "            return 'red'\n", "        else:\n", "            return 'darkred'\n", "    \n", "    # Points interpolés\n", "    for _, row in sample_data.iterrows():\n", "        folium.CircleMarker(\n", "            location=[row['lat'], row['lon']],\n", "            radius=3,\n", "            popup=f\"SPI-3: {row['spi_3_pred']:.2f}<br>Cat<PERSON><PERSON><PERSON>: {row['drought_category']}\",\n", "            color=get_color(row['spi_3_pred']),\n", "            fillColor=get_color(row['spi_3_pred']),\n", "            fillOpacity=0.7\n", "        ).add_to(m)\n", "    \n", "    # Stations météo\n", "    for _, row in stations_df.iterrows():\n", "        folium.Marker(\n", "            location=[row['latitude'], row['longitude']],\n", "            popup=f\"<b>{row['station']}</b><br>SPI-3: {row['spi_3']:.2f}<br>Catégorie: {classify_drought(row['spi_3'])}\",\n", "            icon=folium.Icon(color='black', icon='info-sign')\n", "        ).add_to(m)\n", "    \n", "    return m\n", "\n", "# Création de la carte interactive\n", "drought_map = create_drought_map(centroids_north, spi_july_2016)\n", "drought_map.save('results/carte_secheresse_juillet_2016.html')\n", "print(\"🗺️ Carte interactive sauvegardée: results/carte_secheresse_juillet_2016.html\")\n", "\n", "# Affichage de la carte\n", "drought_map"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Module 3 : Création de l'Aléa Sécheresse CLIMADA (30 min)\n", "\n", "### 3.1 Transformation SPI en aléa CLIMADA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création de l'aléa sécheresse CLIMADA\n", "hazard_drought = Hazard('DR')  # DR = Drought\n", "\n", "# Configuration des centroids\n", "hazard_drought.centroids.set_lat_lon(centroids_north['lat'].values, \n", "                                     centroids_north['lon'].values)\n", "\n", "# Transformation SPI en intensité d'aléa\n", "# Convention: intensité = -SPI (plus SPI est négatif, plus l'intensité est forte)\n", "intensity_values = -centroids_north['spi_3_pred'].values\n", "intensity_values = np.maximum(intensity_values, 0)  # Seulement valeurs positives\n", "\n", "# Matrice d'intensité (1 événement)\n", "intensity_matrix = intensity_values.reshape(1, -1)\n", "hazard_drought.intensity = intensity_matrix\n", "\n", "# Configuration des événements\n", "hazard_drought.event_id = np.array([1])\n", "hazard_drought.event_name = ['Secheresse_Nord_CI_Juillet2016']\n", "hazard_drought.date = np.array([20160701])  # Format YYYYMMDD\n", "\n", "# Fréquence basée sur l'analyse historique (estimation)\n", "# <PERSON><PERSON><PERSON><PERSON><PERSON> sé<PERSON>ère ≈ période de retour 10 ans\n", "hazard_drought.frequency = np.array([0.1])  # 1/10 ans\n", "\n", "# Matrice de fraction (tous les centroids affectés)\n", "hazard_drought.fraction = np.ones_like(intensity_matrix)\n", "\n", "# Vérification de la cohérence\n", "hazard_drought.check()\n", "\n", "print(\"✅ ALÉA SÉCHERESSE CRÉÉ AVEC SUCCÈS!\")\n", "print(f\"Nombre de centroids: {hazard_drought.centroids.size}\")\n", "print(f\"Nombre d'événements: {hazard_drought.size[0]}\")\n", "print(f\"Intensité min: {hazard_drought.intensity.min():.3f}\")\n", "print(f\"Intensité max: {hazard_drought.intensity.max():.3f}\")\n", "print(f\"Intensité moyenne: {hazard_drought.intensity.mean():.3f}\")\n", "print(f\"Fréquence événement: {hazard_drought.frequency[0]:.3f} /an (T={1/hazard_drought.frequency[0]:.0f} ans)\")\n", "\n", "# Statistiques par niveau d'intensité\n", "print(f\"\\n📊 RÉPARTITION DES INTENSITÉS:\")\n", "intensity_flat = intensity_values[intensity_values > 0]\n", "if len(intensity_flat) > 0:\n", "    print(f\"Zones affectées: {len(intensity_flat):,} / {len(intensity_values):,} ({len(intensity_flat)/len(intensity_values)*100:.1f}%)\")\n", "    print(f\"Intensité moyenne (zones affectées): {intensity_flat.mean():.3f}\")\n", "    \n", "    # Classification par seuils\n", "    moderate = np.sum((intensity_flat >= 1) & (intensity_flat < 1.5))\n", "    severe = np.sum((intensity_flat >= 1.5) & (intensity_flat < 2))\n", "    extreme = np.sum(intensity_flat >= 2)\n", "    \n", "    print(f\"\\n🌡️ RÉPARTITION PAR SÉVÉRITÉ:\")\n", "    print(f\"  <PERSON><PERSON><PERSON><PERSON><PERSON> modé<PERSON> (1.0-1.5): {moderate:,} points ({moderate/len(intensity_flat)*100:.1f}%)\")\n", "    print(f\"  <PERSON><PERSON>cher<PERSON><PERSON> sévère (1.5-2.0): {severe:,} points ({severe/len(intensity_flat)*100:.1f}%)\")\n", "    print(f\"  Sécheresse extrême (>2.0): {extreme:,} points ({extreme/len(intensity_flat)*100:.1f}%)\")\n", "else:\n", "    print(\"Aucune zone affectée par la sécheresse\")"]}], "metadata": {"kernelspec": {"display_name": "climada", "language": "python", "name": "climada"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}