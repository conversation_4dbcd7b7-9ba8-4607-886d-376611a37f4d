# Guide d'illustration pour la Présentation CLIMADA
## Emplacements précis pour images, graphiques et exemples visuels

---

## **SECTION 1 : CONTEXTE ET JUSTIFICATION**
### **Emplacement illustrations recommandées :**

**Page 3 - Défi climatique pour la Côte d'Ivoire**
- 🖼️ **GRAPHIQUE 1** : Courbe évolution PIB vs impacts climatiques historiques (1990-2024)
- 🖼️ **CARTE 1** : Carte de vulnérabilité climatique CI avec zones à risque (inondations côtières, sécheresse nord)
- 📊 **INFOGRAPHIE** : "13% de réduction du PIB d'ici 2050" - avec icônes sectoriels

**Page 4 - CLIMADA : L'outil de référence**
- 🖼️ **LOGO/INTERFACE** : Interface CLIMADA avec workflow principal
- 📊 **DIAGRAMME** : Schéma "Aléa × Exposition × Vulnérabilité = Risque"
- 🌍 **CARTE MONDIALE** : Institutions utilisant CLIMADA (ETH Zurich, Banque Mondiale, etc.)

---

## **SECTION 2 : OBJECTIFS PÉDAGOGIQUES**
### **Emplacement illustrations recommandées :**

**Page 6 - Objectifs spécifiques**
- 📊 **FLOWCHART** : Parcours d'apprentissage en 7 étapes (installation → intégration)
- 🎯 **ICONS SET** : Icônes pour chaque compétence (Python, modélisation, calibration, etc.)

---

## **SECTION 3 : MÉTHODOLOGIE PÉDAGOGIQUE**
### **Emplacement illustrations recommandées :**

**Page 8 - Approche progressive**
- 📊 **CAMEMBERT** : Répartition 30% théorie / 50% pratique / 20% application
- 🖼️ **PHOTOS** : Participants en formation sur ordinateurs, écrans avec Jupyter

**Page 9 - Outils pédagogiques**
- 🖼️ **CAPTURES ÉCRAN** : Interface Jupyter Notebook avec code CLIMADA
- 📊 **TIMELINE** : Planning 5 jours avec jalons clés
- 🗺️ **CARTE CI** : Localisation études de cas (Abidjan, zones agricoles nord)

---

## **SECTION 4 : PROGRAMME DÉTAILLÉ**
### **Emplacement illustrations pour chaque jour :**

### **JOUR 1 : Fondamentaux et installation**
**Page 11 - Module 1.1 Cadre conceptuel**
- 🖼️ **DIAGRAMME CONCEPTUEL** : Triangle risque climatique avec exemples CI
- 📊 **GRAPHIQUE** : Historique événements extrêmes CI (1980-2024)
- 🖼️ **PHOTOS** : Dégâts inondations Abidjan 2018, sécheresse cultures 2016

**Page 12 - Module 1.4 Premier exemple**
- 🖼️ **CAPTURE ÉCRAN** : Code Python installation CLIMADA
- 📊 **GRAPHIQUE SORTIE** : Premier calcul impact cyclones Atlantique
- ⚙️ **CODE BLOCK** : Commandes d'installation avec résultats

### **JOUR 2 : Modélisation des aléas**
**Page 14 - Module 2.1 Aléas prioritaires**
- 🗺️ **CARTE MULTI-COUCHES** : Aléas CI (inondations, sécheresse, érosion côtière)
- 📊 **GRAPHIQUES CLIMATIQUES** : 
  * Évolution précipitations Abidjan (1960-2024)
  * Indices sécheresse nord CI (SPI, SPEI)
- 🖼️ **PHOTOS TERRAIN** : Stations SODEXAM, impacts érosion côtière

**Page 15 - Module 2.3 Import données**
- 🖼️ **CAPTURE JUPYTER** : Import et traitement données SODEXAM
- 📊 **CARTES INTERACTIVES** : Visualisation aléas sur grille CI
- 💻 **CODE COMMENTÉ** : Création objet Hazard avec données locales

### **JOUR 3 : Exposition économique**
**Page 17 - Module 3.1 Actifs économiques**
- 📊 **GRAPHIQUES SECTORIELS** :
  * Répartition PIB par secteur avec vulnérabilité climatique
  * Carte valeur foncière Abidjan par commune
- 🗺️ **CARTE ÉCONOMIQUE** : Localisation actifs stratégiques (ports, aéroports, zones industrielles)

**Page 18 - Module 3.3 Fonctions vulnérabilité**
- 📊 **COURBES DOMMAGE** : Fonctions impact calibrées CI
  * Résidentiel moderne vs traditionnel (inondations)
  * Cacao vs coton (sécheresse)
- 🖼️ **CAPTURES JUPYTER** : Code création fonctions d'impact
- 📈 **GRAPHIQUES VALIDATION** : Calibration avec données historiques ONPC

### **JOUR 4 : Calibration et adaptation**
**Page 20 - Module 4.1 Données historiques**
- 📊 **TABLEAUX SINISTRES** : Données ONPC 2000-2024 par type d'événement
- 📈 **COURBES TENDANCES** : Évolution dommages économiques
- 🗺️ **CARTES RISQUE** : Hotspots historiques d'impacts

**Page 21 - Module 4.3 Types d'adaptation**
- 🖼️ **PHOTOS MESURES** : Digues Abidjan, bassins rétention, mangroves
- 📊 **GRAPHIQUE COÛT-BÉNÉFICE** : Analyse économique mesures d'adaptation
- 💰 **INFOGRAPHIES** : ROI système d'alerte précoce

### **JOUR 5 : Applications avancées**
**Page 23 - Module 5.1 Scripts automatisés**
- 💻 **CAPTURE IDE** : Scripts Python automatisation rapport DRB
- 📊 **DASHBOARD** : Interface visualisation temps réel
- 📈 **GRAPHIQUES SORTIE** : Cartes risque automatisées

**Page 24 - Module 5.3 Intégration DRB**
- 📋 **TEMPLATE DRB** : Format rapport FMI avec sections CLIMADA
- 📊 **INDICATEURS CLÉS** : PAM, VaR, stress tests climatiques
- 🎯 **FLOWCHART** : Workflow intégration planification budgétaire

---

## **SECTION 5 : RESSOURCES PÉDAGOGIQUES**
### **Emplacement illustrations recommandées :**

**Page 26 - Supports fournis**
- 🖼️ **CAPTURES DOCUMENTS** : Pages manuel utilisateur, notebooks
- 💾 **ICÔNES FICHIERS** : Types de données (CSV, NetCDF, Shapefile)
- 📱 **INTERFACES** : API SODEXAM, portails données

**Page 27 - Outils techniques**
- 🖼️ **ENVIRONNEMENT TECHNIQUE** : Architecture logicielle (Conda, Python, CLIMADA)
- ⚙️ **DIAGRAMME FLUX** : Pipeline données → modèles → résultats

---

## **SECTION 6 : ÉVALUATION**
### **Emplacement illustrations recommandées :**

**Page 29 - Modalités d'évaluation**
- 📊 **GRAPHIQUE RÉPARTITION** : 40% TP + 40% projet + 20% présentation
- 📋 **GRILLES ÉVALUATION** : Critères notation avec exemples

---

## **SECTION 7 : PERSPECTIVES**
### **Emplacement illustrations recommandées :**

**Page 32 - Applications institutionnelles**
- 🗺️ **ROADMAP** : Timeline intégration CLIMADA dans processus DGE
- 🌐 **RÉSEAU PARTENAIRES** : Connexions UEMOA, institutions régionales
- 📈 **GRAPHIQUES IMPACT** : Projections utilisation 2025-2030

---

## **RECOMMANDATIONS TECHNIQUES VISUELLES**

### **Palette couleurs cohérente :**
- Bleu institutionnel : #1f4e79 (DGE, gouvernement)
- Orange action : #d67228 (urgence, adaptation)  
- Vert données : #70ad47 (agriculture, environnement)
- Rouge risque : #c5504b (aléas, dommages)

### **Types de visualisations prioritaires :**
1. **Cartes géographiques** : 60% des illustrations (très important pour CLIMADA)
2. **Graphiques temporels** : 25% (évolutions climatiques, impacts)
3. **Diagrammes conceptuels** : 10% (workflow, architecture)
4. **Photos terrain** : 5% (contexte réel, validation)

### **Formats techniques :**
- Résolution : 1920x1080 minimum pour projection
- Format : PNG avec transparence ou SVG pour schémas
- Polices : Arial/Calibri (compatibilité universelle)
- Taille texte : 14pt minimum (lisibilité salle formation)

---

## **CHECKLIST VALIDATION VISUELLES**
- [ ] Chaque concept clé illustré par au moins un visuel
- [ ] Progression visuelle cohérente sur les 5 jours
- [ ] Exemples concrets CI dans chaque section
- [ ] Code Python visible et exécutable
- [ ] Données réelles ou réalistes (pas d'exemples théoriques)
- [ ] Résultats CLIMADA authentiques affichés
- [ ] Interface utilisateur CLIMADA montrée
- [ ] Connexion claire avec objectifs FMI/DRB