{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# TP 1 : Modélisation des inondations urbaines à Abidjan\n", "## Événements de juin 2018 - Formation CLIMADA DGE Côte d'Ivoire\n", "\n", "**Objectifs du TP :**\n", "- Créer un aléa inondation à partir de données météorologiques réelles\n", "- Modéliser l'exposition économique d'Abidjan\n", "- Calculer les impacts économiques et les comparer aux dommages observés\n", "- Analyser les mesures d'adaptation possibles\n", "\n", "**Du<PERSON><PERSON> estimée :** 3 heures\n", "\n", "**Données utilisées :** \n", "- Précipitations station Port-Bouët (SODEXAM) juin 2018\n", "- Exposition économique communes Abidjan\n", "- Données sinistres ONPC (18 milliards FCFA de dommages observés)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 1. Configuration de l'environnement et chargement des bibliothèques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration initiale\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Bibliothèques essentielles\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import geopandas as gpd\n", "from shapely.geometry import Point, Polygon\n", "\n", "# Bibliothèques CLIMADA\n", "from climada.hazard import Hazard\n", "from climada.entity import Exposures, ImpactFunc, ImpactFuncSet\n", "from climada.engine import Impact\n", "from climada.util.coordinates import get_grid_points\n", "\n", "# Configuration graphiques\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "%matplotlib inline\n", "\n", "print(\"✅ Environnement configuré avec succès\")\n", "print(f\"Version NumPy: {np.__version__}\")\n", "print(f\"Version Pandas: {pd.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 2. Chargement et exploration des données météorologiques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement des données pluviométriques juin 2018\n", "flood_data = pd.read_csv('inondations_abidjan_juin_2018.csv', parse_dates=['date'])\n", "\n", "print(\"📊 DONNÉES MÉTÉOROLOGIQUES JUIN 2018\")\n", "print(\"=\" * 50)\n", "print(f\"Période: {flood_data['date'].min().strftime('%d/%m/%Y')} - {flood_data['date'].max().strftime('%d/%m/%Y')}\")\n", "print(f\"Nombre de jours: {len(flood_data)}\")\n", "print(f\"Station: {flood_data['station_id'].iloc[0]} ({flood_data['latitude'].iloc[0]:.3f}, {flood_data['longitude'].iloc[0]:.3f})\")\n", "\n", "# Statistiques descriptives\n", "print(\"\\n📈 STATISTIQUES PLUVIOMÉTRIQUES:\")\n", "print(f\"Total mois: {flood_data['precipitation_mm'].sum():.1f} mm\")\n", "print(f\"Moyenne quotidienne: {flood_data['precipitation_mm'].mean():.1f} mm\")\n", "print(f\"Maximum quotidien: {flood_data['precipitation_mm'].max():.1f} mm\")\n", "print(f\"Jours avec pluie: {(flood_data['precipitation_mm'] > 0).sum()} jours\")\n", "\n", "# Identification événements extrêmes\n", "extreme_events = flood_data[flood_data['precipitation_mm'] > 80]\n", "print(f\"\\n🌊 ÉVÉNEMENTS EXTRÊMES (>80mm):\")\n", "for _, event in extreme_events.iterrows():\n", "    print(f\"  - {event['date'].strftime('%d/%m/%Y')}: {event['precipitation_mm']:.1f} mm\")\n", "\n", "# Affichage des premières lignes\n", "print(\"\\n📋 APERÇU DES DONNÉES:\")\n", "display(flood_data.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 3. Visualisation de l'événement météorologique"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Graphique série temporelle précipitations\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))\n", "\n", "# Graphique 1: Précipitations quotidiennes\n", "ax1.bar(flood_data['date'], flood_data['precipitation_mm'], \n", "        color=['red' if x > 80 else 'steelblue' for x in flood_data['precipitation_mm']],\n", "        alpha=0.7, edgecolor='black', linewidth=0.5)\n", "ax1.set_title('Précipitations quotidiennes - Abidjan Port-Bouët - Juin 2018\\n(Événements extrêmes en rouge)', \n", "              fontsize=14, fontweight='bold')\n", "ax1.set_ylabel('Précipitations (mm)', fontsize=12)\n", "ax1.grid(True, alpha=0.3)\n", "ax1.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='Seuil extrême (80mm)')\n", "ax1.legend()\n", "\n", "# Annotation événements majeurs\n", "for _, event in extreme_events.iterrows():\n", "    ax1.annotate(f\"{event['precipitation_mm']:.0f}mm\", \n", "                xy=(event['date'], event['precipitation_mm']),\n", "                xytext=(5, 5), textcoords='offset points',\n", "                fontweight='bold', color='red')\n", "\n", "# Graphique 2: Précipitations cumulées\n", "flood_data['precip_cumul'] = flood_data['precipitation_mm'].cumsum()\n", "ax2.plot(flood_data['date'], flood_data['precip_cumul'], \n", "         color='darkblue', linewidth=2, marker='o', markersize=4)\n", "ax2.set_title('Précipitations cumulées - Juin 2018', fontsize=14, fontweight='bold')\n", "ax2.set_xlabel('Date', fontsize=12)\n", "ax2.set_ylabel('Précipitations cumulées (mm)', fontsize=12)\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Annotation total\n", "ax2.text(flood_data['date'].iloc[-3], flood_data['precip_cumul'].iloc[-1] - 50,\n", "         f\"Total: {flood_data['precip_cumul'].iloc[-1]:.0f}mm\",\n", "         bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"yellow\", alpha=0.7),\n", "         fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Analyse de l'événement du 18-19 juin\n", "event_18_19 = flood_data[flood_data['date'].isin(['2018-06-18', '2018-06-19'])]\n", "total_event = event_18_19['precipitation_mm'].sum()\n", "\n", "print(f\"\\n🌊 ANALYSE ÉVÉNEMENT CRITIQUE 18-19 JUIN:\")\n", "print(f\"Pluie totale sur 2 jours: {total_event:.1f} mm\")\n", "print(f\"Pourcentage du total mensuel: {(total_event/flood_data['precipitation_mm'].sum())*100:.1f}%\")\n", "print(f\"Équivalent à environ {total_event/30:.1f} jours de pluie 'normale'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 4. Création de l'aléa inondation avec CLIMADA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Définition de la zone d'étude (Grand Abidjan)\n", "abidjan_bounds = {\n", "    'min_lat': 5.0, 'max_lat': 5.6,\n", "    'min_lon': -4.2, 'max_lon': -3.8\n", "}\n", "\n", "print(\"🗺️ DÉFINITION ZONE D'ÉTUDE\")\n", "print(f\"Latitude: {abidjan_bounds['min_lat']:.1f}° - {abidjan_bounds['max_lat']:.1f}°N\")\n", "print(f\"Longitude: {abidjan_bounds['min_lon']:.1f}° - {abidjan_bounds['max_lon']:.1f}°W\")\n", "\n", "# Création grille de centroïdes (résolution 1 km)\n", "centroids_abidjan = get_grid_points(\n", "    abidjan_bounds['min_lat'], abidjan_bounds['min_lon'],\n", "    abidjan_bounds['max_lat'], abidjan_bounds['max_lon'],\n", "    res_km=1\n", ")\n", "\n", "print(f\"\\n📐 GRILLE SPATIALE:\")\n", "print(f\"Nombre de centroïdes: {len(centroids_abidjan)}\")\n", "print(f\"Résolution: 1 km × 1 km\")\n", "print(f\"Surface couverte: ~{(len(centroids_abidjan) * 1):.0f} km²\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fonction de conversion pluie → hauteur d'inondation\n", "def rainfall_to_flood_depth(rainfall_mm, drainage_capacity=50, location_factor=1.0):\n", "    \"\"\"\n", "    Modèle empirique simple : pluie → hauteur inondation\n", "    \n", "    Parameters:\n", "    - rainfall_mm: précipitation en mm\n", "    - drainage_capacity: capacité drainage en mm (défaut: 50mm)\n", "    - location_factor: facteur géographique (topographie, urbanisation)\n", "    \n", "    Returns:\n", "    - flood_depth en mètres\n", "    \"\"\"\n", "    if rainfall_mm <= drainage_capacity:\n", "        return 0.0\n", "    \n", "    excess_rain = rainfall_mm - drainage_capacity\n", "    # Coefficient empirique calibré sur Abidjan\n", "    base_depth = excess_rain * 0.012  # mm → m\n", "    \n", "    # Application facteur local\n", "    flood_depth = base_depth * location_factor\n", "    \n", "    # Limite physique réaliste\n", "    return min(flood_depth, 3.0)\n", "\n", "# Test de la fonction avec l'événement du 18 juin\n", "max_rainfall = flood_data['precipitation_mm'].max()\n", "test_depth = rainfall_to_flood_depth(max_rainfall)\n", "\n", "print(f\"🧮 TEST MODÈLE HYDROLOGIQUE:\")\n", "print(f\"Pluie maximum: {max_rainfall:.1f} mm\")\n", "print(f\"Hauteur inondation estimée: {test_depth:.2f} m\")\n", "print(f\"Capacité drainage supposée: 50 mm\")\n", "\n", "# Test avec différents facteurs géographiques\n", "print(\"\\n📍 SENSIBILITÉ SELON LOCALISATION:\")\n", "locations = {\n", "    'Zone bien drainée': 0.7,\n", "    'Zone standard': 1.0, \n", "    'Zone mal drainée': 1.3,\n", "    'Zone très vulnérable': 1.8\n", "}\n", "\n", "for location, factor in locations.items():\n", "    depth = rainfall_to_flood_depth(max_rainfall, location_factor=factor)\n", "    print(f\"  {location}: {depth:.2f} m\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création de l'objet Hazard CLIMADA\n", "from scipy import sparse\n", "\n", "# Sélection des événements d'inondation (seuil 30 mm)\n", "flood_threshold = 30\n", "flood_events = flood_data[flood_data['precipitation_mm'] >= flood_threshold].copy()\n", "\n", "print(f\"🌊 ÉVÉNEMENTS D'INONDATION RETENUS (≥{flood_threshold}mm):\")\n", "print(f\"Nombre d'événements: {len(flood_events)}\")\n", "for idx, event in flood_events.iterrows():\n", "    depth = rainfall_to_flood_depth(event['precipitation_mm'])\n", "    print(f\"  {event['date'].strftime('%d/%m')}: {event['precipitation_mm']:.1f}mm → {depth:.2f}m\")\n", "\n", "# Construction matrice intensité (événements × centroïdes)\n", "n_events = len(flood_events)\n", "n_centroids = len(centroids_abidjan)\n", "\n", "print(f\"\\n📊 MATRICE D'INTENSITÉ:\")\n", "print(f\"Dimensions: {n_events} événements × {n_centroids} centroïdes\")\n", "\n", "intensity_matrix = np.zeros((n_events, n_centroids))\n", "\n", "# Remplissage matrice avec variation spatiale\n", "np.random.seed(42)  # Reproductibilité\n", "\n", "for i, (idx, event) in enumerate(flood_events.iterrows()):\n", "    base_depth = rainfall_to_flood_depth(event['precipitation_mm'])\n", "    \n", "    for j, centroid in enumerate(centroids_abidjan):\n", "        lat, lon = centroid[1], centroid[0]\n", "        \n", "        # Variation spatiale selon distance station météo\n", "        dist_station = ((lat - 5.261)**2 + (lon + 3.936)**2)**0.5\n", "        distance_factor = max(0.7, 1 - dist_station * 0.3)\n", "        \n", "        # Facteur topographique aléatoire (zones basses plus inondées)\n", "        topo_factor = np.random.uniform(0.8, 1.4)\n", "        \n", "        # Facteur urbanisation (drainage variable)\n", "        urban_factor = np.random.uniform(0.9, 1.2)\n", "        \n", "        # Intensité finale\n", "        final_intensity = base_depth * distance_factor * topo_factor * urban_factor\n", "        intensity_matrix[i, j] = max(0, min(final_intensity, 3.0))\n", "\n", "print(f\"Intensité moyenne: {intensity_matrix.mean():.3f} m\")\n", "print(f\"Intensité maximum: {intensity_matrix.max():.3f} m\")\n", "print(f\"Pixels avec inondation (>0): {(intensity_matrix > 0).sum()}/{intensity_matrix.size}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création objet Hazard CLIMADA\n", "hazard_flood = Hazard()\n", "\n", "# Configuration hazard\n", "hazard_flood.tag = {\n", "    'haz_type': 'FL',\n", "    'description': 'Inondations urbaines Abidjan - Événement juin 2018',\n", "    'file_name': 'flood_abidjan_june2018.hdf5'\n", "}\n", "hazard_flood.units = 'm'  # Hauteur d'eau en mètres\n", "\n", "# Centroïdes\n", "hazard_flood.centroids.set_lat_lon(\n", "    centroids_abidjan[:, 1],  # latitudes\n", "    centroids_abidjan[:, 0]   # longitudes\n", ")\n", "\n", "# Événements\n", "hazard_flood.event_id = np.arange(1, n_events + 1)\n", "hazard_flood.date = flood_events['date'].values\n", "\n", "# Fréquences (événements historiques observés)\n", "# Approche simple: 1/période de retour estimée\n", "frequencies = []\n", "for _, event in flood_events.iterrows():\n", "    if event['precipitation_mm'] > 150:  # Très extrême\n", "        freq = 1/20  # Une fois tous les 20 ans\n", "    elif event['precipitation_mm'] > 80:  # Extrême\n", "        freq = 1/10  # Une fois tous les 10 ans\n", "    elif event['precipitation_mm'] > 50:  # Fort\n", "        freq = 1/5   # Une fois tous les 5 ans\n", "    else:  # <PERSON><PERSON><PERSON><PERSON>\n", "        freq = 1/2   # Une fois tous les 2 ans\n", "    frequencies.append(freq)\n", "\n", "hazard_flood.frequency = np.array(frequencies)\n", "\n", "# Matrice intensité (convertie en sparse matrix)\n", "hazard_flood.intensity = sparse.csr_matrix(intensity_matrix)\n", "\n", "# Validation\n", "hazard_flood.check()\n", "\n", "print(\"✅ HAZARD INONDATION CRÉÉ AVEC SUCCÈS\")\n", "print(f\"Type d'aléa: {hazard_flood.tag['haz_type']}\")\n", "print(f\"Nombre d'événements: {hazard_flood.size[0]}\")\n", "print(f\"Nombre de centroïdes: {hazard_flood.size[1]}\")\n", "print(f\"Unité intensité: {hazard_flood.units}\")\n", "print(f\"Fréquences: {hazard_flood.frequency}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 5. Modélisation de l'exposition économique"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement données exposition Abidjan\n", "exposure_data = pd.read_csv('exposition_economique_abidjan.csv')\n", "\n", "print(\"💰 EXPOSITION ÉCONOMIQUE ABIDJAN\")\n", "print(\"=\" * 40)\n", "print(f\"Communes analysées: {len(exposure_data)}\")\n", "\n", "# Calcul exposition totale\n", "exposure_data['total_value_billion'] = (\n", "    exposure_data['residential_value_billion_fcfa'] + \n", "    exposure_data['commercial_value_billion_fcfa'] + \n", "    exposure_data['infrastructure_value_billion_fcfa']\n", ")\n", "\n", "total_exposure = exposure_data['total_value_billion'].sum()\n", "print(f\"\\n💵 VALEURS EXPOSÉES (milliards FCFA):\")\n", "print(f\"  Résidentiel: {exposure_data['residential_value_billion_fcfa'].sum():,.0f}\")\n", "print(f\"  Commercial: {exposure_data['commercial_value_billion_fcfa'].sum():,.0f}\")\n", "print(f\"  Infrastructure: {exposure_data['infrastructure_value_billion_fcfa'].sum():,.0f}\")\n", "print(f\"  TOTAL: {total_exposure:,.0f} milliards FCFA\")\n", "\n", "# Top 5 communes par exposition\n", "print(f\"\\n🏆 TOP 5 COMMUNES PAR EXPOSITION:\")\n", "top_communes = exposure_data.nlargest(5, 'total_value_billion')\n", "for _, commune in top_communes.iterrows():\n", "    print(f\"  {commune['commune']:.<15} {commune['total_value_billion']:>8,.0f} milliards FCFA\")\n", "\n", "# Analyse par niveau de risque\n", "print(f\"\\n🌊 EXPOSITION PAR NIVEAU DE RISQUE INONDATION:\")\n", "risk_analysis = exposure_data.groupby('flood_risk_zone').agg({\n", "    'total_value_billion': 'sum',\n", "    'population': 'sum'\n", "}).round(0)\n", "\n", "for risk_level, data in risk_analysis.iterrows():\n", "    pct_value = (data['total_value_billion'] / total_exposure) * 100\n", "    print(f\"  {risk_level:.<15} {data['total_value_billion']:>8,.0f} milliards ({pct_value:.1f}%)\")\n", "\n", "display(exposure_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Localisation géographique des communes (coordonnées approximatives)\n", "commune_coordinates = {\n", "    'Cocody': (5.347, -3.987),\n", "    'Yopougon': (5.340, -4.088), \n", "    'Adjamé': (5.352, -4.017),\n", "    'Port-Bouët': (5.235, -3.920),\n", "    'Attécoubé': (5.330, -4.040),\n", "    'Plateau': (5.325, -4.013),\n", "    'Treichville': (5.293, -4.013),\n", "    'Marcory': (5.286, -3.983),\n", "    '<PERSON><PERSON><PERSON>': (5.267, -3.963),\n", "    'Abobo': (5.417, -4.017)\n", "}\n", "\n", "# Ajout coordonnées aux données d'exposition\n", "exposure_data['latitude'] = exposure_data['commune'].map(lambda x: commune_coordinates.get(x, (5.3, -4.0))[0])\n", "exposure_data['longitude'] = exposure_data['commune'].map(lambda x: commune_coordinates.get(x, (5.3, -4.0))[1])\n", "\n", "print(\"📍 COORDONNÉES GÉOGRAPHIQUES AJOUTÉES\")\n", "print(exposure_data[['commune', 'latitude', 'longitude', 'total_value_billion']].head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création GeoDataFrame pour CLIMADA\n", "geometry = [Point(lon, lat) for lat, lon in zip(exposure_data['latitude'], exposure_data['longitude'])]\n", "exposure_gdf = gpd.GeoDataFrame(exposure_data, geometry=geometry)\n", "\n", "# Transformation pour CLIMADA Exposures\n", "exposures_abidjan = Exposures(exposure_gdf)\n", "\n", "# Configuration exposition pour CLIMADA\n", "exposures_abidjan.gdf['value'] = exposure_data['total_value_billion'] * 1e9  # Conversion milliards → FCFA\n", "exposures_abidjan.gdf['category_id'] = 1  # <PERSON><PERSON><PERSON><PERSON> urbaine\n", "exposures_abidjan.gdf['region_id'] = range(1, len(exposure_data) + 1)\n", "exposures_abidjan.gdf['impf_FL'] = 1  # ID fonction d'impact inondation\n", "\n", "# Métadonnées\n", "exposures_abidjan.tag = {\n", "    'description': 'Exposition économique urbaine Abidjan',\n", "    'category': 'urban_multi_sector',\n", "    'file_name': 'exposure_abidjan_2024.hdf5'\n", "}\n", "exposures_abidjan.value_unit = 'FCFA'\n", "\n", "# Validation\n", "exposures_abidjan.check()\n", "\n", "print(\"✅ EXPOSITION CLIMADA CRÉÉE AVEC SUCCÈS\")\n", "print(f\"Nombre de points d'exposition: {len(exposures_abidjan.gdf)}\")\n", "print(f\"Valeur totale: {exposures_abidjan.gdf['value'].sum():,.0f} FCFA\")\n", "print(f\"Valeur moyenne par point: {exposures_abidjan.gdf['value'].mean():,.0f} FCFA\")\n", "print(f\"Unité de valeur: {exposures_abidjan.value_unit}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 6. Définition des fonctions de vulnérabilité"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création fonction d'impact pour inondations urbaines\n", "# Basée sur les retours d'expérience des inondations Abidjan 2018\n", "\n", "impact_func_urban = ImpactFunc()\n", "impact_func_urban.id = 1\n", "impact_func_urban.name = 'Urbain_mixte_inondation_Abidjan'\n", "impact_func_urban.intensity_unit = 'm'\n", "impact_func_urban.haz_type = 'FL'\n", "\n", "# Définition de la courbe de dommage\n", "# Calibrée sur la base des 18 milliards FCFA de dommages observés en 2018\n", "\n", "# <PERSON><PERSON> d'eau (mètres)\n", "water_depths = np.array([0, 0.1, 0.3, 0.5, 1.0, 1.5, 2.0, 3.0])\n", "\n", "# Mean Damage Degree (proportion de la valeur détruite)\n", "# Ajusté pour correspondre aux observations 2018\n", "damage_ratios = np.array([0, 0.02, 0.08, 0.18, 0.35, 0.55, 0.70, 0.85])\n", "\n", "# Percentage of Affected Assets (proportion des biens touchés)\n", "affected_assets = np.array([0, 0.3, 0.6, 0.8, 1.0, 1.0, 1.0, 1.0])\n", "\n", "# Attribution des valeurs\n", "impact_func_urban.intensity = water_depths\n", "impact_func_urban.mdd = damage_ratios\n", "impact_func_urban.paa = affected_assets\n", "\n", "print(\"🛠️ FONCTION D'IMPACT CRÉÉE\")\n", "print(f\"Nom: {impact_func_urban.name}\")\n", "print(f\"Type d'aléa: {impact_func_urban.haz_type}\")\n", "print(f\"Unité intensité: {impact_func_urban.intensity_unit}\")\n", "\n", "print(\"\\n📊 COURBE DE DOMMAGE:\")\n", "for i in range(len(water_depths)):\n", "    print(f\"  {water_depths[i]:>4.1f}m → {damage_ratios[i]*100:>5.1f}% dommage ({affected_assets[i]*100:>5.1f}% touchés)\")\n", "\n", "# Création du set de fonctions d'impact\n", "impact_func_set = ImpactFuncSet()\n", "impact_func_set.append(impact_func_urban)\n", "impact_func_set.check()\n", "\n", "print(\"\\n✅ SET DE FONCTIONS D'IMPACT VALIDÉ\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation de la fonction de dommage\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Graphique 1: Mean Damage Degree\n", "ax1.plot(water_depths, damage_ratios * 100, 'o-', linewidth=3, markersize=8, color='red')\n", "ax1.set_xlabel('Hauteur d\\'eau (m)', fontsize=12)\n", "ax1.set_ylabel('Dom<PERSON> moyen (%)', fontsize=12)\n", "ax1.set_title('<PERSON><PERSON>be de Dommage\\n(Mean Damage Degree)', fontsize=14, fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "ax1.set_ylim(0, 100)\n", "\n", "# Annotations points clés\n", "key_points = [(0.5, damage_ratios[3]*100), (1.0, damage_ratios[4]*100), (2.0, damage_ratios[6]*100)]\n", "for x, y in key_points:\n", "    ax1.annotate(f'{y:.0f}%', xy=(x, y), xytext=(5, 5), \n", "                textcoords='offset points', fontweight='bold')\n", "\n", "# Graphique 2: Percentage Affected Assets\n", "ax2.plot(water_depths, affected_assets * 100, 's-', linewidth=3, markersize=8, color='blue')\n", "ax2.set_xlabel('Hauteur d\\'eau (m)', fontsize=12)\n", "ax2.set_ylabel('Actifs affectés (%)', fontsize=12)\n", "ax2.set_title('Proportion d\\'Actifs Touchés\\n(Percentage Affected Assets)', fontsize=14, fontweight='bold')\n", "ax2.grid(True, alpha=0.3)\n", "ax2.set_ylim(0, 110)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calcul dommage effectif (MDD × PAA)\n", "effective_damage = damage_ratios * affected_assets\n", "print(\"\\n📈 DOMMAGE EFFECTIF (MDD × PAA):\")\n", "for i in range(len(water_depths)):\n", "    print(f\"  {water_depths[i]:>4.1f}m → {effective_damage[i]*100:>5.1f}% de perte totale\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 7. Calcul des impacts économiques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calcul de l'impact avec CLIMADA\n", "print(\"🧮 CALCUL DES IMPACTS ÉCONOMIQUES...\")\n", "print(\"(Cette étape peut prendre quelques secondes)\")\n", "\n", "# Impact calculation\n", "impact_flood = Impact()\n", "impact_flood.calc(\n", "    exposures=exposures_abidjan,\n", "    impact_funcs=impact_func_set,\n", "    hazard=hazard_flood\n", ")\n", "\n", "print(\"\\n✅ CALCUL TERMINÉ\")\n", "\n", "# Résultats principaux\n", "total_damage = impact_flood.at_event.sum()  # Dommages totaux tous événements\n", "aai = impact_flood.aai_agg  # Average Annual Impact (Perte Annuelle Moyenne)\n", "max_event_damage = impact_flood.at_event.max()  # Dommage maximum d'un événement\n", "\n", "print(f\"\\n💰 RÉSULTATS D'IMPACT - INONDATIONS ABIDJAN:\")\n", "print(\"=\" * 55)\n", "print(f\"<PERSON><PERSON> Annuel<PERSON> (PAM): {aai:,.0f} FCFA\")\n", "print(f\"Dommage total simulé: {total_damage:,.0f} FCFA\")\n", "print(f\"Dommage max un événement: {max_event_damage:,.0f} FCFA\")\n", "\n", "# Conversion en milliards pour comparaison\n", "aai_billions = aai / 1e9\n", "total_damage_billions = total_damage / 1e9\n", "max_damage_billions = max_event_damage / 1e9\n", "\n", "print(f\"\\n📊 EN MILLIARDS DE FCFA:\")\n", "print(f\"PAM: {aai_billions:.2f} milliards FCFA/an\")\n", "print(f\"Dommage total: {total_damage_billions:.2f} milliards FCFA\")\n", "print(f\"Dommage max: {max_damage_billions:.2f} milliards FCFA\")\n", "\n", "# Comparaison avec les observations 2018\n", "observed_damage_2018 = 18  # milliards FCFA (source ONPC)\n", "print(f\"\\n🎯 VALIDATION AVEC OBSERVATIONS 2018:\")\n", "print(f\"Dommages observés: {observed_damage_2018} milliards FCFA\")\n", "print(f\"Dommages simulés (événement max): {max_damage_billions:.1f} milliards FCFA\")\n", "print(f\"Écart: {abs(observed_damage_2018 - max_damage_billions):.1f} milliards FCFA\")\n", "print(f\"Précision du modèle: {(1 - abs(observed_damage_2018 - max_damage_billions)/observed_damage_2018)*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyse détaillée des impacts par événement\n", "print(\"📋 IMPACTS PAR ÉVÉNEMENT:\")\n", "print(\"=\" * 50)\n", "\n", "# Création DataFrame résultats\n", "results_df = pd.DataFrame({\n", "    'date': flood_events['date'].values,\n", "    'precipitation_mm': flood_events['precipitation_mm'].values,\n", "    'damage_fcfa': impact_flood.at_event,\n", "    'damage_billions': impact_flood.at_event / 1e9,\n", "    'frequency': hazard_flood.frequency\n", "})\n", "\n", "# Tri par dommage décroissant\n", "results_df = results_df.sort_values('damage_billions', ascending=False)\n", "\n", "print(\"Top événements par impact économique:\")\n", "for i, (_, event) in enumerate(results_df.head().iterrows()):\n", "    print(f\"{i+1:2d}. {event['date'].strftime('%d/%m/%Y')}: {event['precipitation_mm']:>6.1f}mm → {event['damage_billions']:>8.2f} milliards FCFA\")\n", "\n", "# Statistiques\n", "print(f\"\\n📊 STATISTIQUES:\")\n", "print(f\"Dommage moyen par événement: {results_df['damage_billions'].mean():.2f} milliards FCFA\")\n", "print(f\"Médiane: {results_df['damage_billions'].median():.2f} milliards FCFA\")\n", "print(f\"Écart-type: {results_df['damage_billions'].std():.2f} milliards FCFA\")\n", "\n", "display(results_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation des résultats d'impact\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Graphique 1: Dommages par événement\n", "bars = ax1.bar(range(len(results_df)), results_df['damage_billions'], \n", "               color=['red' if x > 10 else 'orange' if x > 5 else 'steelblue' for x in results_df['damage_billions']])\n", "ax1.set_xlabel('Événements (classés par impact)', fontsize=11)\n", "ax1.set_ylabel('Dommages (milliards FCFA)', fontsize=11)\n", "ax1.set_title('Dommages Économiques par Événement', fontsize=13, fontweight='bold')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Annotation événement maximum\n", "max_idx = results_df['damage_billions'].idxmax()\n", "max_date = results_df.loc[max_idx, 'date']\n", "max_damage = results_df['damage_billions'].max()\n", "ax1.annotate(f'Max: {max_damage:.1f}Md\\n{max_date.strftime(\"%d/%m\")}', \n", "            xy=(0, max_damage), xytext=(5, 5),\n", "            textcoords='offset points', fontweight='bold')\n", "\n", "# Graphique 2: Relation pluie-dommages\n", "ax2.scatter(results_df['precipitation_mm'], results_df['damage_billions'], \n", "           s=100, alpha=0.7, c=results_df['damage_billions'], cmap='Reds')\n", "ax2.set_xlabel('Précipitations (mm)', fontsize=11)\n", "ax2.set_ylabel('Dommages (milliards FCFA)', fontsize=11)\n", "ax2.set_title('Relation Précipitations - Dommages Économiques', fontsize=13, fontweight='bold')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Graphique 3: Distribution des dommages\n", "ax3.hist(results_df['damage_billions'], bins=8, alpha=0.7, color='skyblue', edgecolor='black')\n", "ax3.axvline(results_df['damage_billions'].mean(), color='red', linestyle='--', \n", "           label=f'Moyenne: {results_df[\"damage_billions\"].mean():.2f}Md')\n", "ax3.set_xlabel('Dommages (milliards FCFA)', fontsize=11)\n", "ax3.set_ylabel('Nombre d\\'événements', fontsize=11)\n", "ax3.set_title('Distribution des Dommages', fontsize=13, fontweight='bold')\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Graphique 4: <PERSON>mp<PERSON><PERSON> mod<PERSON><PERSON> vs observations\n", "categories = ['Observ<PERSON>\\n(2018)', '<PERSON><PERSON><PERSON>\\n(max)', '<PERSON><PERSON><PERSON>\\n(moyen)']\n", "values = [observed_damage_2018, max_damage_billions, results_df['damage_billions'].mean()]\n", "colors = ['green', 'red', 'orange']\n", "\n", "bars = ax4.bar(categories, values, color=colors, alpha=0.7)\n", "ax4.set_ylabel('Dommages (milliards FCFA)', fontsize=11)\n", "ax4.set_title('Validation Mo<PERSON> vs Observations', fontsize=13, fontweight='bold')\n", "ax4.grid(True, alpha=0.3)\n", "\n", "# Annotations\n", "for bar, value in zip(bars, values):\n", "    ax4.annotate(f'{value:.1f}Md', xy=(bar.get_x() + bar.get_width()/2, value),\n", "                ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 8. <PERSON><PERSON><PERSON> des mesures d'adaptation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simulation de mesures d'adaptation\n", "print(\"🛡️ SIMULATION MESURES D'ADAPTATION\")\n", "print(\"=\" * 50)\n", "\n", "# Scénarios d'adaptation\n", "adaptation_scenarios = {\n", "    'scenario_base': {\n", "        'name': 'Situation actuelle (sans adaptation)',\n", "        'reduction_factor': 1.0,\n", "        'cost_billions': 0\n", "    },\n", "    'scenario_drainage': {\n", "        'name': 'Amélioration drainage urbain',\n", "        'reduction_factor': 0.75,  # 25% réduction dommages\n", "        'cost_billions': 120\n", "    },\n", "    'scenario_comprehensive': {\n", "        'name': 'Programme complet (drainage + digues + alerte)',\n", "        'reduction_factor': 0.55,  # 45% réduction dommages\n", "        'cost_billions': 350\n", "    },\n", "    'scenario_optimal': {\n", "        'name': 'Solution optimale intégrée',\n", "        'reduction_factor': 0.40,  # 60% réduction dommages\n", "        'cost_billions': 530\n", "    }\n", "}\n", "\n", "# Calculs pour chaque scénario\n", "adaptation_results = []\n", "\n", "for scenario_id, scenario in adaptation_scenarios.items():\n", "    # Création fonction d'impact adaptée\n", "    impact_func_adapted = ImpactFunc()\n", "    impact_func_adapted.id = 2\n", "    impact_func_adapted.name = f'Urbain_adapté_{scenario_id}'\n", "    impact_func_adapted.intensity_unit = 'm'\n", "    impact_func_adapted.haz_type = 'FL'\n", "    \n", "    # Réduction des dommages\n", "    impact_func_adapted.intensity = impact_func_urban.intensity\n", "    impact_func_adapted.mdd = impact_func_urban.mdd * scenario['reduction_factor']\n", "    impact_func_adapted.paa = impact_func_urban.paa  # Proportion touchée identique\n", "    \n", "    # Set de fonctions adapté\n", "    impact_func_set_adapted = ImpactFuncSet()\n", "    impact_func_set_adapted.append(impact_func_adapted)\n", "    \n", "    # Calcul impact adapté\n", "    impact_adapted = Impact()\n", "    impact_adapted.calc(\n", "        exposures=exposures_abidjan,\n", "        impact_funcs=impact_func_set_adapted,\n", "        hazard=hazard_flood\n", "    )\n", "    \n", "    # Résultats\n", "    aai_adapted = impact_adapted.aai_agg / 1e9  # Milliards FCFA\n", "    max_damage_adapted = impact_adapted.at_event.max() / 1e9\n", "    \n", "    # Bénéfices (dommages évités)\n", "    aai_avoided = aai_billions - aai_adapted\n", "    max_avoided = max_damage_billions - max_damage_adapted\n", "    \n", "    adaptation_results.append({\n", "        'scenario': scenario['name'],\n", "        'cost_billions': scenario['cost_billions'],\n", "        'aai_adapted': aai_adapted,\n", "        'max_damage_adapted': max_damage_adapted,\n", "        'aai_avoided': aai_avoided,\n", "        'max_avoided': max_avoided,\n", "        'reduction_factor': scenario['reduction_factor']\n", "    })\n", "\n", "# Affichage résultats\n", "print(\"\\n📊 RÉSULTATS PAR SCÉNARIO D'ADAPTATION:\")\n", "print(\"-\" * 80)\n", "\n", "for result in adaptation_results:\n", "    print(f\"\\n🔹 {result['scenario']}:\")\n", "    print(f\"   Coût: {result['cost_billions']:>8.0f} milliards FCFA\")\n", "    print(f\"   PAM résiduelle: {result['aai_adapted']:>8.2f} milliards FCFA/an\")\n", "    print(f\"   Dommages évités/an: {result['aai_avoided']:>8.2f} milliards FCFA\")\n", "    print(f\"   Réduction: {(1-result['reduction_factor'])*100:>8.0f}%\")\n", "    \n", "    if result['cost_billions'] > 0:\n", "        bcr = result['aai_avoided'] / (result['cost_billions'] * 0.1)  # Coût annualisé 10%\n", "        print(f\"   Ratio coût-bénéfice: {bcr:>8.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> co<PERSON>t-b<PERSON><PERSON><PERSON>e d<PERSON>e\n", "print(\"\\n💰 ANALYSE COÛT-BÉNÉFICE (horizon 20 ans)\")\n", "print(\"=\" * 55)\n", "\n", "# Paramètres économiques\n", "horizon_years = 20\n", "discount_rate = 0.08  # Taux d'actualisation 8%\n", "maintenance_rate = 0.03  # Coût maintenance 3%/an\n", "\n", "cba_results = []\n", "\n", "for result in adaptation_results[1:]:  # Exclure scénario base\n", "    # Coûts\n", "    initial_cost = result['cost_billions']\n", "    annual_maintenance = initial_cost * maintenance_rate\n", "    \n", "    # Bénéfices annuels (dommages évités)\n", "    annual_benefits = result['aai_avoided']\n", "    \n", "    # Valeur actuelle nette (VAN)\n", "    discount_factors = [(1 + discount_rate)**(-t) for t in range(1, horizon_years + 1)]\n", "    \n", "    pv_benefits = sum(annual_benefits * df for df in discount_factors)\n", "    pv_maintenance = sum(annual_maintenance * df for df in discount_factors)\n", "    pv_costs = initial_cost + pv_maintenance\n", "    \n", "    npv = pv_benefits - pv_costs\n", "    bcr = pv_benefits / pv_costs if pv_costs > 0 else 0\n", "    \n", "    # Période de retour sur investissement\n", "    payback_years = initial_cost / annual_benefits if annual_benefits > 0 else float('inf')\n", "    \n", "    cba_results.append({\n", "        'scenario': result['scenario'],\n", "        'initial_cost': initial_cost,\n", "        'annual_benefits': annual_benefits,\n", "        'npv': npv,\n", "        'bcr': bcr,\n", "        'payback_years': payback_years\n", "    })\n", "    \n", "    print(f\"\\n🎯 {result['scenario']}:\")\n", "    print(f\"   Investissement initial: {initial_cost:>8.0f} milliards FCFA\")\n", "    print(f\"   Bénéfices annuels: {annual_benefits:>8.2f} milliards FCFA\")\n", "    print(f\"   VAN (20 ans, 8%): {npv:>8.0f} milliards FCFA\")\n", "    print(f\"   Ratio B/C: {bcr:>8.2f}\")\n", "    print(f\"   Retour investissement: {payback_years:>8.1f} ans\")\n", "    \n", "    if npv > 0:\n", "        print(f\"   ✅ Projet rentable\")\n", "    else:\n", "        print(f\"   ❌ Projet non rentable\")\n", "\n", "# Recommandation\n", "best_scenario = max(cba_results, key=lambda x: x['bcr'] if x['npv'] > 0 else 0)\n", "print(f\"\\n🏆 RECOMMANDATION:\")\n", "print(f\"Mei<PERSON>ur scénario: {best_scenario['scenario']}\")\n", "print(f\"<PERSON>io coût-bénéfice: {best_scenario['bcr']:.2f}\")\n", "print(f\"VAN: {best_scenario['npv']:.0f} milliards FCFA\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualisation analyse coût-bénéfice\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Graphique 1: Coûts vs Bénéfices\n", "scenarios = [r['scenario'] for r in cba_results]\n", "costs = [r['initial_cost'] for r in cba_results]\n", "benefits_annual = [r['annual_benefits'] for r in cba_results]\n", "\n", "x = np.arange(len(scenarios))\n", "width = 0.35\n", "\n", "ax1.bar(x - width/2, costs, width, label='Coût initial', color='red', alpha=0.7)\n", "ax1.bar(x + width/2, [b*10 for b in benefits_annual], width, \n", "        label='Bénéfices×10 (annuels)', color='green', alpha=0.7)\n", "ax1.set_xlabel('<PERSON>én<PERSON><PERSON> d\\'adaptation', fontsize=11)\n", "ax1.set_ylabel('Milliards FCFA', fontsize=11)\n", "ax1.set_title('Coûts d\\'Investissement vs Bénéfices Annuels', fontsize=13, fontweight='bold')\n", "ax1.set_xticks(x)\n", "ax1.set_xticklabels([s.replace(' ', '\\n') for s in scenarios], fontsize=9)\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Graphique 2: <PERSON><PERSON><PERSON>\n", "bcrs = [r['bcr'] for r in cba_results]\n", "colors = ['green' if bcr > 1 else 'red' for bcr in bcrs]\n", "\n", "bars = ax2.bar(scenarios, bcrs, color=colors, alpha=0.7)\n", "ax2.axhline(y=1, color='black', linestyle='--', label='Seuil rentabilité')\n", "ax2.set_xlabel('<PERSON>én<PERSON><PERSON> d\\'adaptation', fontsize=11)\n", "ax2.set_ylabel('Ratio Bénéfice/Coût', fontsize=11)\n", "ax2.set_title('<PERSON>ios <PERSON>-Bénéfice par Scénario', fontsize=13, fontweight='bold')\n", "ax2.set_xticklabels([s.replace(' ', '\\n') for s in scenarios], fontsize=9)\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "# Annotations\n", "for bar, bcr in zip(bars, bcrs):\n", "    ax2.annotate(f'{bcr:.2f}', xy=(bar.get_x() + bar.get_width()/2, bcr),\n", "                ha='center', va='bottom', fontweight='bold')\n", "\n", "# Graphique 3: VAN\n", "npvs = [r['npv'] for r in cba_results]\n", "colors_npv = ['green' if npv > 0 else 'red' for npv in npvs]\n", "\n", "bars = ax3.bar(scenarios, npvs, color=colors_npv, alpha=0.7)\n", "ax3.axhline(y=0, color='black', linestyle='-', alpha=0.8)\n", "ax3.set_xlabel('<PERSON>én<PERSON><PERSON> d\\'adaptation', fontsize=11)\n", "ax3.set_ylabel('VAN (milliards FCFA)', fontsize=11)\n", "ax3.set_title('Valeur Actuelle <PERSON>te (20 ans, 8%)', fontsize=13, fontweight='bold')\n", "ax3.set_xticklabels([s.replace(' ', '\\n') for s in scenarios], fontsize=9)\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Graphique 4: <PERSON><PERSON><PERSON><PERSON> de retour\n", "paybacks = [min(r['payback_years'], 25) for r in cba_results]  # Cap à 25 ans pour visualisation\n", "colors_pb = ['green' if pb <= 10 else 'orange' if pb <= 15 else 'red' for pb in paybacks]\n", "\n", "bars = ax4.bar(scenarios, paybacks, color=colors_pb, alpha=0.7)\n", "ax4.set_xlabel('<PERSON>én<PERSON><PERSON> d\\'adaptation', fontsize=11)\n", "ax4.set_ylabel('Années', fontsize=11)\n", "ax4.set_title('Période de Retour sur Investissement', fontsize=13, fontweight='bold')\n", "ax4.set_xticklabels([s.replace(' ', '\\n') for s in scenarios], fontsize=9)\n", "ax4.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "## 9. Synthèse et recommandations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Synthèse finale des résultats\n", "print(\"📋 SYNTHÈSE - MODÉLISATION INONDATIONS ABIDJAN JUIN 2018\")\n", "print(\"=\" * 65)\n", "\n", "print(\"\\n🌊 ALÉA MODÉLISÉ:\")\n", "print(f\"   • Événements analysés: {len(flood_events)} (≥30mm de pluie)\")\n", "print(f\"   • Événement maximum: {flood_data['precipitation_mm'].max():.0f}mm (18 juin 2018)\")\n", "print(f\"   • Hauteur inondation max simulée: {intensity_matrix.max():.2f}m\")\n", "print(f\"   • Résolution spatiale: 1 km × 1 km ({len(centroids_abidjan)} centroïdes)\")\n", "\n", "print(\"\\n💰 EXPOSITION ÉCONOMIQUE:\")\n", "print(f\"   • Valeur totale exposée: {total_exposure:,.0f} milliards FCFA\")\n", "print(f\"   • Communes analysées: {len(exposure_data)}\")\n", "print(f\"   • Population exposée: {exposure_data['population'].sum():,.0f} habitants\")\n", "print(f\"   • Zones à risque élevé/extrême: {len(exposure_data[exposure_data['flood_risk_zone'].isin(['Élevé', 'Extrême'])])} communes\")\n", "\n", "print(\"\\n📊 IMPACTS CALCULÉS:\")\n", "print(f\"   • <PERSON><PERSON> Annuelle Moyenne: {aai_billions:.2f} milliards FCFA/an\")\n", "print(f\"   • Dommage maximum simulé: {max_damage_billions:.1f} milliards FCFA\")\n", "print(f\"   • Dommages observés 2018: {observed_damage_2018} milliards FCFA\")\n", "print(f\"   • Précision du modèle: {(1 - abs(observed_damage_2018 - max_damage_billions)/observed_damage_2018)*100:.0f}%\")\n", "\n", "print(\"\\n🛡️ ADAPTATION RECOMMANDÉE:\")\n", "best = max(cba_results, key=lambda x: x['bcr'] if x['npv'] > 0 else 0)\n", "print(f\"   • <PERSON><PERSON><PERSON> optimal: {best['scenario']}\")\n", "print(f\"   • Investissement: {best['initial_cost']:.0f} milliards FCFA\")\n", "print(f\"   • <PERSON><PERSON>-bénéfice: {best['bcr']:.2f}\")\n", "print(f\"   • VAN (20 ans): {best['npv']:.0f} milliards FCFA\")\n", "print(f\"   • Retour sur investissement: {best['payback_years']:.1f} ans\")\n", "\n", "print(\"\\n🎯 RECOMMANDATIONS POUR LA DGE:\")\n", "print(\"   1. Intégrer ces estimations dans la Déclaration des Risques Budgétaires\")\n", "print(\"   2. Provisionner annuellement {:.0f} milliards FCFA pour les inondations\".format(aai_billions * 1.2))\n", "print(\"   3. Lancer l'étude de faisabilité du programme d'adaptation optimal\")\n", "print(\"   4. Développer un système de suivi temps réel avec SODEXAM\")\n", "print(\"   5. Mettre à jour le modèle annuellement avec nouvelles données\")\n", "\n", "print(\"\\n✅ MODÈLE VALIDÉ ET OPÉRATIONNEL POUR AIDE À LA DÉCISION\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export des résultats pour reporting\n", "print(\"💾 EXPORT DES RÉSULTATS\")\n", "print(\"=\" * 30)\n", "\n", "# Sauvegarde résultats principaux\n", "results_summary = pd.DataFrame({\n", "    'Indicateur': [\n", "        '<PERSON><PERSON> (milliards FCFA)',\n", "        'Dommage maximum simulé (milliards FCFA)',\n", "        'Dommages observés 2018 (milliards FCFA)',\n", "        'Précision modèle (%)',\n", "        'Exposition totale (milliards FCFA)',\n", "        'Coût adaptation optimale (milliards FCFA)',\n", "        'Ratio coût-bénéfice optimal',\n", "        'VAN adaptation optimale (milliards FCFA)'\n", "    ],\n", "    'Valeur': [\n", "        f\"{aai_billions:.2f}\",\n", "        f\"{max_damage_billions:.1f}\",\n", "        f\"{observed_damage_2018}\",\n", "        f\"{(1 - abs(observed_damage_2018 - max_damage_billions)/observed_damage_2018)*100:.0f}\",\n", "        f\"{total_exposure:,.0f}\",\n", "        f\"{best['initial_cost']:.0f}\",\n", "        f\"{best['bcr']:.2f}\",\n", "        f\"{best['npv']:.0f}\"\n", "    ]\n", "})\n", "\n", "# Sauve<PERSON>e\n", "results_summary.to_csv('resultats_inondations_abidjan_2018.csv', index=False, encoding='utf-8')\n", "results_df.to_csv('impacts_par_evenement_abidjan.csv', index=False, encoding='utf-8')\n", "\n", "print(\"✅ Fichiers exportés:\")\n", "print(\"   • resultats_inondations_abidjan_2018.csv\")\n", "print(\"   • impacts_par_evenement_abidjan.csv\")\n", "\n", "print(\"\\n📈 Résultats principaux:\")\n", "display(results_summary)\n", "\n", "print(\"\\n🎓 TP TERMINÉ - FÉLICITATIONS !\")\n", "print(\"Vous avez modélisé avec succès les inondations d'Abidjan avec CLIMADA\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 4}