{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# TP1 : Modélisation des Inondations d'Abidjan - Juin 2018\n",
    "## Formation CLIMADA - Direction Générale de l'Économie (DGE)\n",
    "\n",
    "### Objectifs du TP\n",
    "1. **Ma<PERSON><PERSON>ser** le workflow CLIMADA complet\n",
    "2. **Manipuler** des données météorologiques réelles de SODEXAM\n",
    "3. **<PERSON><PERSON>er** un aléa inondation spatialisé\n",
    "4. **Mod<PERSON>liser** l'exposition économique urbaine d'Abidjan\n",
    "5. **Calculer** et **valider** les impacts économiques\n",
    "\n",
    "### Contexte\n",
    "Les 19-20 juin 2018, Abidjan a subi des inondations exceptionnelles causées par des précipitations record (223mm en 24h à Port-Bouët). L'ONPC a estimé les dommages à 18 milliards FCFA. Ce TP vise à reproduire ces résultats avec CLIMADA.\n",
    "\n",
    "### Durée estimée : 3 heures\n",
    "\n",
    "---"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Module 1 : Configuration et Import des Données (45 min)\n",
    "\n",
    "### 1.1 Import des bibliothèques nécessaires"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Bibliothèques de base\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from datetime import datetime, timedelta\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Bibliothèques géospatiales\n",
    "import geopandas as gpd\n",
    "import rasterio as rio\n",
    "from shapely.geometry import Point, Polygon\n",
    "import folium\n",
    "\n",
    "# CLIMADA core\n",
    "from climada.hazard import Hazard\n",
    "from climada.entity import Exposures, ImpactFunc, ImpactFuncSet\n",
    "from climada.engine import Impact\n",
    "from climada.util.coordinates import get_grid_points\n",
    "\n",
    "# Configuration affichage\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "%matplotlib inline\n",
    "\n",
    "print(\"✅ Toutes les bibliothèques importées avec succès!\")\n",
    "print(f\"Version CLIMADA: {climada.__version__}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 1.2 Chargement des données météorologiques SODEXAM"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Chargement données précipitations Port-Bouët juin 2018\n",
    "# Note: En formation réelle, ces données seront fournies par SODEXAM\n",
    "precip_data = pd.read_csv('data/port_bouet_juin_2018.csv')\n",
    "\n",
    "# Conversion colonne datetime\n",
    "precip_data['datetime'] = pd.to_datetime(precip_data['datetime'])\n",
    "precip_data.set_index('datetime', inplace=True)\n",
    "\n",
    "# Affichage informations générales\n",
    "print(\"📊 DONNÉES MÉTÉOROLOGIQUES SODEXAM - PORT-BOUËT\")\n",
    "print(f\"Période: {precip_data.index.min()} à {precip_data.index.max()}\")\n",
    "print(f\"Nombre d'observations: {len(precip_data)}\")\n",
    "print(f\"Fréquence: {precip_data.index.freq or 'Variable'}\")\n",
    "\n",
    "# Statistiques descriptives\n",
    "print(\"\\n📈 STATISTIQUES PRÉCIPITATIONS:\")\n",
    "print(precip_data['precip_mm'].describe())\n",
    "\n",
    "# Affichage premières lignes\n",
    "print(\"\\n🔍 APERÇU DES DONNÉES:\")\n",
    "display(precip_data.head(10))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 1.3 Analyse exploratoire des données"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Calcul cumuls sur différentes durées\n",
    "precip_data['precip_1h'] = precip_data['precip_mm']\n",
    "precip_data['precip_3h'] = precip_data['precip_mm'].rolling(window=3).sum()\n",
    "precip_data['precip_6h'] = precip_data['precip_mm'].rolling(window=6).sum()\n",
    "precip_data['precip_12h'] = precip_data['precip_mm'].rolling(window=12).sum()\n",
    "precip_data['precip_24h'] = precip_data['precip_mm'].rolling(window=24).sum()\n",
    "\n",
    "# Identification de l'événement extrême du 19-20 juin\n",
    "event_start = '2018-06-19 00:00:00'\n",
    "event_end = '2018-06-20 23:59:59'\n",
    "event_data = precip_data.loc[event_start:event_end]\n",
    "\n",
    "print(\"🌧️ ÉVÉNEMENT EXTRÊME 19-20 JUIN 2018:\")\n",
    "print(f\"Cumul total 48h: {event_data['precip_mm'].sum():.1f} mm\")\n",
    "print(f\"Maximum 24h: {event_data['precip_24h'].max():.1f} mm\")\n",
    "print(f\"Maximum 1h: {event_data['precip_1h'].max():.1f} mm\")\n",
    "print(f\"Heure du pic: {event_data['precip_1h'].idxmax()}\")\n",
    "\n",
    "# Graphique série temporelle\n",
    "fig, axes = plt.subplots(2, 1, figsize=(15, 10))\n",
    "\n",
    "# Graphique 1: Précipitations horaires juin 2018\n",
    "axes[0].bar(precip_data.index, precip_data['precip_1h'], \n",
    "           color='steelblue', alpha=0.7, width=0.03)\n",
    "axes[0].axvspan(pd.to_datetime(event_start), pd.to_datetime(event_end), \n",
    "               alpha=0.3, color='red', label='Événement extrême')\n",
    "axes[0].set_title('Précipitations Horaires - Port-Bouët Juin 2018', fontsize=14, fontweight='bold')\n",
    "axes[0].set_ylabel('Précipitations (mm/h)')\n",
    "axes[0].legend()\n",
    "axes[0].grid(True, alpha=0.3)\n",
    "\n",
    "# Graphique 2: Cumuls glissants pendant l'événement\n",
    "event_data[['precip_1h', 'precip_3h', 'precip_6h', 'precip_24h']].plot(ax=axes[1])\n",
    "axes[1].set_title('Cumuls Glissants pendant l\\'Événement (19-20 juin)', fontsize=14, fontweight='bold')\n",
    "axes[1].set_ylabel('Précipitations cumulées (mm)')\n",
    "axes[1].legend(['1h', '3h', '6h', '24h'])\n",
    "axes[1].grid(True, alpha=0.3)\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Sauvegarde des résultats\n",
    "max_24h = event_data['precip_24h'].max()\n",
    "print(f\"\\n🎯 VALEUR CLÉ: Maximum 24h = {max_24h:.1f} mm (à comparer avec période de retour)\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 1.4 Analyse fréquentielle - Périodes de retour"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Simulation données historiques pour analyse fréquentielle\n",
    "# En réalité, utiliser 30+ années de données SODEXAM\n",
    "np.random.seed(42)\n",
    "historical_max_24h = np.random.gamma(2, 30, 30)  # Simulation 30 ans de maxima annuels\n",
    "\n",
    "# Ajustement loi de Gumbel\n",
    "from scipy import stats\n",
    "gumbel_params = stats.gumbel_r.fit(historical_max_24h)\n",
    "\n",
    "# Calcul périodes de retour\n",
    "return_periods = [2, 5, 10, 25, 50, 100]\n",
    "quantiles = [1 - 1/T for T in return_periods]\n",
    "precip_return_periods = stats.gumbel_r.ppf(quantiles, *gumbel_params)\n",
    "\n",
    "# Création DataFrame résultats\n",
    "freq_analysis = pd.DataFrame({\n",
    "    'Periode_retour_ans': return_periods,\n",
    "    'Precipitations_24h_mm': precip_return_periods\n",
    "})\n",
    "\n",
    "print(\"📊 ANALYSE FRÉQUENTIELLE - PRÉCIPITATIONS 24H:\")\n",
    "display(freq_analysis.round(1))\n",
    "\n",
    "# Estimation période de retour de l'événement juin 2018\n",
    "prob_event = stats.gumbel_r.cdf(max_24h, *gumbel_params)\n",
    "return_period_event = 1 / (1 - prob_event)\n",
    "\n",
    "print(f\"\\n🎯 PÉRIODE DE RETOUR ÉVÉNEMENT JUIN 2018:\")\n",
    "print(f\"Précipitations observées: {max_24h:.1f} mm\")\n",
    "print(f\"Période de retour estimée: {return_period_event:.0f} ans\")\n",
    "\n",
    "# Graphique courbe IDF simplifiée\n",
    "plt.figure(figsize=(10, 6))\n",
    "plt.semilogx(return_periods, precip_return_periods, 'bo-', linewidth=2, markersize=8)\n",
    "plt.axhline(max_24h, color='red', linestyle='--', linewidth=2, \n",
    "           label=f'Événement juin 2018 ({max_24h:.1f} mm)')\n",
    "plt.xlabel('Période de retour (années)')\n",
    "plt.ylabel('Précipitations 24h (mm)')\n",
    "plt.title('Courbe Intensité-Durée-Fréquence (IDF) - Port-Bouët', fontweight='bold')\n",
    "plt.grid(True, alpha=0.3)\n",
    "plt.legend()\n",
    "plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Module 2 : Création de l'Aléa Inondation (60 min)\n",
    "\n",
    "### 2.1 Transformation pluie-ruissellement (Méthode SCS-CN)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def calculate_runoff_scs(precipitation, curve_number):\n",
    "    \"\"\"\n",
    "    Calcule le ruissellement selon la méthode SCS Curve Number\n",
    "    \n",
    "    Parameters:\n",
    "    -----------\n",
    "    precipitation : float or array\n",
    "        Précipitations en mm\n",
    "    curve_number : float\n",
    "        Numéro de courbe SCS (30-100)\n",
    "        \n",
    "    Returns:\n",
    "    --------\n",
    "    runoff : float or array\n",
    "        Ruissellement en mm\n",
    "    \"\"\"\n",
    "    # Calcul rétention maximale S\n",
    "    S = 25400 / curve_number - 254  # en mm\n",
    "    \n",
    "    # Pertes initiales (abstraction)\n",
    "    Ia = 0.2 * S\n",
    "    \n",
    "    # Calcul ruissellement\n",
    "    runoff = np.where(precipitation > Ia,\n",
    "                     (precipitation - Ia)**2 / (precipitation - Ia + S),\n",
    "                     0)\n",
    "    \n",
    "    return runoff\n",
    "\n",
    "# Définition des CN par type d'occupation du sol Abidjan\n",
    "land_use_cn = {\n",
    "    'urbain_dense': 92,      # Zones très urbanisées (Plateau, Cocody)\n",
    "    'urbain_moyen': 85,      # Zones moyennement urbanisées\n",
    "    'urbain_sparse': 75,     # Zones peu urbanisées (périphérie)\n",
    "    'espaces_verts': 65,     # Parcs, jardins\n",
    "    'lagune': 100,           # Surfaces d'eau\n",
    "    'mangrove': 70           # Zones humides\n",
    "}\n",
    "\n",
    "print(\"🏙️ NUMÉROS DE COURBE (CN) PAR TYPE D'OCCUPATION:\")\n",
    "for land_type, cn in land_use_cn.items():\n",
    "    print(f\"  {land_type}: CN = {cn}\")\n",
    "\n",
    "# Calcul ruissellement pour différents types d'occupation\n",
    "precip_test = max_24h  # Utilisation du maximum observé\n",
    "\n",
    "print(f\"\\n💧 RUISSELLEMENT POUR {precip_test:.1f} mm DE PLUIE:\")\n",
    "for land_type, cn in land_use_cn.items():\n",
    "    runoff = calculate_runoff_scs(precip_test, cn)\n",
    "    runoff_percent = (runoff / precip_test) * 100\n",
    "    print(f\"  {land_type}: {runoff:.1f} mm ({runoff_percent:.1f}% de la pluie)\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 2.2 Création de la grille spatiale Abidjan"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Définition des limites géographiques d'Abidjan\n",
    "abidjan_bounds = {\n",
    "    'min_lat': 5.2,    # Sud (proche océan)\n",
    "    'max_lat': 5.5,    # Nord (vers Anyama)\n",
    "    'min_lon': -4.1,   # Ouest (vers Songon)\n",
    "    'max_lon': -3.8    # Est (vers Bingerville)\n",
    "}\n",
    "\n",
    "# Création grille haute résolution (100m x 100m)\n",
    "resolution_km = 0.1  # 100 mètres\n",
    "centroids_abj = get_grid_points(**abidjan_bounds, res_km=resolution_km)\n",
    "\n",
    "print(f\"🗺️ GRILLE SPATIALE ABIDJAN:\")\n",
    "print(f\"Résolution: {resolution_km*1000:.0f}m x {resolution_km*1000:.0f}m\")\n",
    "print(f\"Nombre de points: {len(centroids_abj):,}\")\n",
    "print(f\"Étendue: {abidjan_bounds['min_lat']:.2f}°N à {abidjan_bounds['max_lat']:.2f}°N\")\n",
    "print(f\"         {abidjan_bounds['min_lon']:.2f}°W à {abidjan_bounds['max_lon']:.2f}°W\")\n",
    "\n",
    "# Simulation occupation du sol (en réalité, utiliser données BNETD)\n",
    "np.random.seed(42)\n",
    "n_points = len(centroids_abj)\n",
    "\n",
    "# Attribution probabiliste des types d'occupation\n",
    "land_use_probs = {\n",
    "    'urbain_dense': 0.3,\n",
    "    'urbain_moyen': 0.4,\n",
    "    'urbain_sparse': 0.2,\n",
    "    'espaces_verts': 0.08,\n",
    "    'lagune': 0.015,\n",
    "    'mangrove': 0.005\n",
    "}\n",
    "\n",
    "land_use_types = list(land_use_probs.keys())\n",
    "land_use_weights = list(land_use_probs.values())\n",
    "\n",
    "centroids_abj['land_use'] = np.random.choice(land_use_types, \n",
    "                                           size=n_points, \n",
    "                                           p=land_use_weights)\n",
    "\n",
    "# Attribution CN correspondant\n",
    "centroids_abj['curve_number'] = centroids_abj['land_use'].map(land_use_cn)\n",
    "\n",
    "# Calcul ruissellement par point de grille\n",
    "centroids_abj['runoff_mm'] = calculate_runoff_scs(precip_test, centroids_abj['curve_number'])\n",
    "\n",
    "print(f\"\\n📊 RÉPARTITION OCCUPATION DU SOL:\")\n",
    "land_use_stats = centroids_abj['land_use'].value_counts(normalize=True) * 100\n",
    "for land_type, percent in land_use_stats.items():\n",
    "    print(f\"  {land_type}: {percent:.1f}%\")\n",
    "\n",
    "print(f\"\\n💧 STATISTIQUES RUISSELLEMENT:\")\n",
    "print(f\"Minimum: {centroids_abj['runoff_mm'].min():.1f} mm\")\n",
    "print(f\"Maximum: {centroids_abj['runoff_mm'].max():.1f} mm\")\n",
    "print(f\"Moyenne: {centroids_abj['runoff_mm'].mean():.1f} mm\")\n",
    "\n",
    "# Affichage aperçu données\n",
    "display(centroids_abj.head())"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "climada",
   "language": "python",
   "name": "climada"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.9.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}

   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 2.3 Création de l'objet Hazard CLIMADA"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Création de l'aléa inondation CLIMADA\n",
    "hazard_flood = Hazard('FL')  # FL = Flood\n",
    "\n",
    "# Configuration des centroids\n",
    "hazard_flood.centroids.set_lat_lon(centroids_abj['lat'].values, \n",
    "                                   centroids_abj['lon'].values)\n",
    "\n",
    "# Attribution des intensités (ruissellement en mm)\n",
    "intensity_matrix = centroids_abj['runoff_mm'].values.reshape(1, -1)\n",
    "hazard_flood.intensity = intensity_matrix\n",
    "\n",
    "# Configuration des événements\n",
    "hazard_flood.event_id = np.array([1])  # Un seul événement\n",
    "hazard_flood.event_name = ['Inondation_Abidjan_Juin2018']\n",
    "hazard_flood.date = np.array([20180620])  # Format YYYYMMDD\n",
    "hazard_flood.frequency = np.array([1/return_period_event])  # Fréquence annuelle\n",
    "\n",
    "# Matrice de fraction (tous les centroids affectés)\n",
    "hazard_flood.fraction = np.ones_like(intensity_matrix)\n",
    "\n",
    "# Vérification de la cohérence\n",
    "hazard_flood.check()\n",
    "\n",
    "print(\"✅ ALÉA INONDATION CRÉÉ AVEC SUCCÈS!\")\n",
    "print(f\"Nombre de centroids: {hazard_flood.centroids.size}\")\n",
    "print(f\"Nombre d'événements: {hazard_flood.size[0]}\")\n",
    "print(f\"Intensité min: {hazard_flood.intensity.min():.2f} mm\")\n",
    "print(f\"Intensité max: {hazard_flood.intensity.max():.2f} mm\")\n",
    "print(f\"Intensité moyenne: {hazard_flood.intensity.mean():.2f} mm\")\n",
    "print(f\"Fréquence événement: {hazard_flood.frequency[0]:.6f} /an (T={1/hazard_flood.frequency[0]:.0f} ans)\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 2.4 Visualisation de l'aléa inondation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Carte interactive avec Folium\n",
    "def create_flood_map(centroids_df, intensity_col='runoff_mm'):\n",
    "    \"\"\"\n",
    "    Crée une carte interactive de l'aléa inondation\n",
    "    \"\"\"\n",
    "    # Centre de la carte sur Abidjan\n",
    "    center_lat = centroids_df['lat'].mean()\n",
    "    center_lon = centroids_df['lon'].mean()\n",
    "    \n",
    "    # Création carte de base\n",
    "    m = folium.Map(location=[center_lat, center_lon], \n",
    "                   zoom_start=11, \n",
    "                   tiles='OpenStreetMap')\n",
    "    \n",
    "    # Échantillonnage pour performance (1 point sur 10)\n",
    "    sample_data = centroids_df.iloc[::10].copy()\n",
    "    \n",
    "    # Normalisation des intensités pour couleurs\n",
    "    max_intensity = sample_data[intensity_col].max()\n",
    "    sample_data['color_intensity'] = sample_data[intensity_col] / max_intensity\n",
    "    \n",
    "    # Ajout des points colorés\n",
    "    for idx, row in sample_data.iterrows():\n",
    "        color_val = row['color_intensity']\n",
    "        if color_val < 0.3:\n",
    "            color = 'green'\n",
    "        elif color_val < 0.6:\n",
    "            color = 'orange'\n",
    "        else:\n",
    "            color = 'red'\n",
    "            \n",
    "        folium.CircleMarker(\n",
    "            location=[row['lat'], row['lon']],\n",
    "            radius=3,\n",
    "            popup=f\"Ruissellement: {row[intensity_col]:.1f} mm<br>Type: {row['land_use']}\",\n",
    "            color=color,\n",
    "            fillColor=color,\n",
    "            fillOpacity=0.7\n",
    "        ).add_to(m)\n",
    "    \n",
    "    # Légende\n",
    "    legend_html = '''\n",
    "    <div style=\"position: fixed; \n",
    "                bottom: 50px; left: 50px; width: 150px; height: 90px; \n",
    "                background-color: white; border:2px solid grey; z-index:9999; \n",
    "                font-size:14px; padding: 10px\">\n",
    "    <p><b>Ruissellement (mm)</b></p>\n",
    "    <p><i class=\"fa fa-circle\" style=\"color:green\"></i> Faible (< 30% max)</p>\n",
    "    <p><i class=\"fa fa-circle\" style=\"color:orange\"></i> Moyen (30-60% max)</p>\n",
    "    <p><i class=\"fa fa-circle\" style=\"color:red\"></i> Fort (> 60% max)</p>\n",
    "    </div>\n",
    "    '''\n",
    "    m.get_root().html.add_child(folium.Element(legend_html))\n",
    "    \n",
    "    return m\n",
    "\n",
    "# Création de la carte\n",
    "flood_map = create_flood_map(centroids_abj)\n",
    "\n",
    "# Sauvegarde de la carte\n",
    "flood_map.save('results/carte_aleas_inondation_abidjan.html')\n",
    "print(\"🗺️ Carte interactive sauvegardée: results/carte_aleas_inondation_abidjan.html\")\n",
    "\n",
    "# Affichage de la carte (si dans Jupyter)\n",
    "flood_map"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Module 3 : Modélisation de l'Exposition Économique (45 min)\n",
    "\n",
    "### 3.1 Chargement des données économiques"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Chargement données exposition économique Abidjan\n",
    "# Note: En formation réelle, ces données proviennent de l'INS\n",
    "exposure_data = pd.read_csv('data/exposition_economique_abidjan.csv')\n",
    "\n",
    "print(\"💰 DONNÉES EXPOSITION ÉCONOMIQUE ABIDJAN (INS):\")\n",
    "print(f\"Nombre d'entités: {len(exposure_data)}\")\n",
    "print(f\"Colonnes disponibles: {list(exposure_data.columns)}\")\n",
    "\n",
    "# Affichage statistiques par commune\n",
    "print(\"\\n📊 VALEUR ÉCONOMIQUE PAR COMMUNE (milliards FCFA):\")\n",
    "commune_stats = exposure_data.groupby('commune')['valeur_fcfa'].sum() / 1e9\n",
    "commune_stats = commune_stats.sort_values(ascending=False)\n",
    "for commune, valeur in commune_stats.items():\n",
    "    print(f\"  {commune}: {valeur:.1f} milliards FCFA\")\n",
    "\n",
    "print(f\"\\n💎 VALEUR TOTALE ABIDJAN: {exposure_data['valeur_fcfa'].sum()/1e12:.1f} billions FCFA\")\n",
    "\n",
    "# Affichage par secteur d'activité\n",
    "print(\"\\n🏢 RÉPARTITION PAR SECTEUR:\")\n",
    "secteur_stats = exposure_data.groupby('secteur')['valeur_fcfa'].sum() / 1e9\n",
    "secteur_percent = (secteur_stats / secteur_stats.sum()) * 100\n",
    "for secteur, valeur in secteur_stats.items():\n",
    "    pct = secteur_percent[secteur]\n",
    "    print(f\"  {secteur}: {valeur:.0f} milliards FCFA ({pct:.1f}%)\")\n",
    "\n",
    "# Aperçu des données\n",
    "display(exposure_data.head())"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 3.2 Géolocalisation et création de l'objet Exposures"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Création des géométries Point\n",
    "from shapely.geometry import Point\n",
    "import geopandas as gpd\n",
    "\n",
    "# Conversion en GeoDataFrame\n",
    "geometry = [Point(xy) for xy in zip(exposure_data['longitude'], exposure_data['latitude'])]\n",
    "gdf_exposure = gpd.GeoDataFrame(exposure_data, geometry=geometry, crs='EPSG:4326')\n",
    "\n",
    "print(f\"🌍 GÉOLOCALISATION EXPOSITION:\")\n",
    "print(f\"Système de coordonnées: {gdf_exposure.crs}\")\n",
    "print(f\"Étendue géographique:\")\n",
    "bounds = gdf_exposure.total_bounds\n",
    "print(f\"  Latitude: {bounds[1]:.3f}° à {bounds[3]:.3f}°\")\n",
    "print(f\"  Longitude: {bounds[0]:.3f}° à {bounds[2]:.3f}°\")\n",
    "\n",
    "# Création de l'objet Exposures CLIMADA\n",
    "exposures = Exposures()\n",
    "\n",
    "# Configuration des données\n",
    "exposures.gdf = gdf_exposure.copy()\n",
    "exposures.gdf['value'] = exposures.gdf['valeur_fcfa']  # CLIMADA attend une colonne 'value'\n",
    "\n",
    "# Attribution des centroids les plus proches\n",
    "exposures.set_centroids(hazard_flood.centroids)\n",
    "\n",
    "# Vérification\n",
    "exposures.check()\n",
    "\n",
    "print(f\"\\n✅ OBJET EXPOSURES CRÉÉ:\")\n",
    "print(f\"Nombre d'actifs exposés: {exposures.gdf.shape[0]}\")\n",
    "print(f\"Valeur totale: {exposures.gdf['value'].sum()/1e12:.2f} billions FCFA\")\n",
    "print(f\"Valeur moyenne par actif: {exposures.gdf['value'].mean()/1e6:.1f} millions FCFA\")\n",
    "print(f\"Centroids assignés: {len(exposures.gdf['centr_FL'].unique())} uniques\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 3.3 Analyse spatiale de l'exposition"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Agrégation par commune pour analyse\n",
    "commune_exposure = exposures.gdf.groupby('commune').agg({\n",
    "    'value': ['sum', 'count', 'mean'],\n",
    "    'latitude': 'mean',\n",
    "    'longitude': 'mean'\n",
    "}).round(2)\n",
    "\n",
    "# Aplatissement des colonnes multi-niveaux\n",
    "commune_exposure.columns = ['valeur_totale', 'nombre_actifs', 'valeur_moyenne', 'lat_centre', 'lon_centre']\n",
    "commune_exposure['valeur_milliards'] = commune_exposure['valeur_totale'] / 1e9\n",
    "\n",
    "# Tri par valeur décroissante\n",
    "commune_exposure = commune_exposure.sort_values('valeur_totale', ascending=False)\n",
    "\n",
    "print(\"🏙️ EXPOSITION ÉCONOMIQUE PAR COMMUNE:\")\n",
    "print(\"=\"*80)\n",
    "print(f\"{'Commune':<15} {'Valeur (Mds)':<12} {'Nb Actifs':<10} {'Moy/Actif (M)':<15}\")\n",
    "print(\"=\"*80)\n",
    "for commune, row in commune_exposure.iterrows():\n",
    "    valeur_mds = row['valeur_milliards']\n",
    "    nb_actifs = int(row['nombre_actifs'])\n",
    "    moy_millions = row['valeur_moyenne'] / 1e6\n",
    "    print(f\"{commune:<15} {valeur_mds:<12.1f} {nb_actifs:<10} {moy_millions:<15.1f}\")\n",
    "\n",
    "# Graphique en secteurs par commune\n",
    "plt.figure(figsize=(12, 8))\n",
    "\n",
    "# Graphique 1: Répartition par commune\n",
    "plt.subplot(2, 2, 1)\n",
    "commune_values = commune_exposure['valeur_milliards']\n",
    "plt.pie(commune_values, labels=commune_values.index, autopct='%1.1f%%', startangle=90)\n",
    "plt.title('Répartition Exposition par Commune\\n(milliards FCFA)', fontweight='bold')\n",
    "\n",
    "# Graphique 2: Répartition par secteur\n",
    "plt.subplot(2, 2, 2)\n",
    "secteur_values = exposures.gdf.groupby('secteur')['value'].sum() / 1e9\n",
    "plt.pie(secteur_values, labels=secteur_values.index, autopct='%1.1f%%', startangle=90)\n",
    "plt.title('Répartition Exposition par Secteur\\n(milliards FCFA)', fontweight='bold')\n",
    "\n",
    "# Graphique 3: Distribution des valeurs\n",
    "plt.subplot(2, 2, 3)\n",
    "plt.hist(exposures.gdf['value']/1e6, bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n",
    "plt.xlabel('Valeur par actif (millions FCFA)')\n",
    "plt.ylabel('Fréquence')\n",
    "plt.title('Distribution des Valeurs d\\'Actifs', fontweight='bold')\n",
    "plt.yscale('log')\n",
    "\n",
    "# Graphique 4: Densité spatiale\n",
    "plt.subplot(2, 2, 4)\n",
    "plt.scatter(exposures.gdf['longitude'], exposures.gdf['latitude'], \n",
    "           c=exposures.gdf['value']/1e6, cmap='Reds', alpha=0.6, s=20)\n",
    "plt.colorbar(label='Valeur (millions FCFA)')\n",
    "plt.xlabel('Longitude')\n",
    "plt.ylabel('Latitude')\n",
    "plt.title('Distribution Spatiale Exposition', fontweight='bold')\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# Sauvegarde des résultats\n",
    "commune_exposure.to_csv('results/exposition_par_commune.csv')\n",
    "print(\"\\n💾 Résultats sauvegardés: results/exposition_par_commune.csv\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
