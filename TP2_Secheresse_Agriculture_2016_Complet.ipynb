{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# TP2 : Modélisation de la Sécheresse Agricole - Nord Côte d'Ivoire 2016\n", "## Formation CLIMADA - Direction Générale de l'Économie (DGE)\n", "\n", "### Objectifs du TP\n", "1. **Calculer** les indices SPI (Standardized Precipitation Index) multi-stations\n", "2. **Spatialiser** la sécheresse par interpolation krigeage\n", "3. **<PERSON><PERSON><PERSON><PERSON><PERSON>** l'exposition agricole multi-cultures (coton, riz, maïs)\n", "4. **Évaluer** les impacts économiques sectoriels\n", "5. **Analyser** des scénarios d'adaptation\n", "\n", "### Contexte\n", "La saison agricole 2016 a été marquée par une sécheresse sévère dans le Nord de la Côte d'Ivoire, affectant particulièrement les cultures de coton, riz pluvial et maïs. Ce TP vise à quantifier ces impacts avec CLIMADA.\n", "\n", "### <PERSON><PERSON><PERSON> estimée : 3 heures\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Module 1 : Données Agro-Météorologiques et Calcul SPI (45 min)\n", "\n", "### 1.1 Import des bibliothèques et configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bibliothèques de base\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Bibliothèques géospatiales\n", "import geopandas as gpd\n", "import rasterio as rio\n", "from shapely.geometry import Point, Polygon\n", "import folium\n", "\n", "# Bibliothèques statistiques\n", "from scipy import stats\n", "from sklearn.gaussian_process import GaussianProcessRegressor\n", "from sklearn.gaussian_process.kernels import RBF, Matern\n", "from sklearn.metrics import mean_squared_error\n", "\n", "# CLIMADA core\n", "from climada.hazard import Hazard\n", "from climada.entity import Exposures, ImpactFunc, ImpactFuncSet\n", "from climada.engine import Impact\n", "from climada.util.coordinates import get_grid_points\n", "\n", "# Configuration affichage\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"RdYlBu_r\")  # Palette adaptée à la sécheresse\n", "%matplotlib inline\n", "\n", "print(\"✅ Toutes les bibliothèques importées avec succès!\")\n", "print(f\"Version CLIMADA: {climada.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Chargement des données météorologiques multi-stations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement données sécheresse 2016 - Nord Côte d'Ivoire\n", "# Note: En formation réelle, ces données proviennent de SODEXAM\n", "drought_data = pd.read_csv('data/secheresse_agriculture_2016.csv')\n", "\n", "# Conversion colonne datetime\n", "drought_data['date'] = pd.to_datetime(drought_data['date'])\n", "\n", "# Informations sur les stations\n", "stations_info = drought_data.groupby('station').agg({\n", "    'latitude': 'first',\n", "    'longitude': 'first',\n", "    'altitude': 'first',\n", "    'precip_mm': 'count'\n", "}).rename(columns={'precip_mm': 'nb_observations'})\n", "\n", "print(\"🌧️ DONNÉES MÉTÉOROLOGIQUES MULTI-STATIONS - NORD CI:\")\n", "print(f\"Période: {drought_data['date'].min()} à {drought_data['date'].max()}\")\n", "print(f\"Nombre de stations: {drought_data['station'].nunique()}\")\n", "print(f\"Nombre total d'observations: {len(drought_data):,}\")\n", "\n", "print(\"\\n📍 INFORMATIONS STATIONS:\")\n", "print(\"=\"*80)\n", "print(f\"{'Station':<15} {'Latitude':<10} {'Longitude':<11} {'Altitude':<9} {'Obs.':<6}\")\n", "print(\"=\"*80)\n", "for station, info in stations_info.iterrows():\n", "    print(f\"{station:<15} {info['latitude']:<10.3f} {info['longitude']:<11.3f} {info['altitude']:<9.0f} {info['nb_observations']:<6}\")\n", "\n", "# Vérification complétude des données\n", "print(\"\\n📊 COMPLÉTUDE DES DONNÉES PAR STATION:\")\n", "for station in drought_data['station'].unique():\n", "    station_data = drought_data[drought_data['station'] == station]\n", "    total_months = len(station_data)\n", "    missing_data = station_data['precip_mm'].isna().sum()\n", "    completeness = ((total_months - missing_data) / total_months) * 100\n", "    print(f\"  {station}: {completeness:.1f}% complète ({missing_data} valeurs manquantes)\")\n", "\n", "# Aperçu des données\n", "display(drought_data.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Calcul des indices SPI (Standardized Precipitation Index)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_spi(precip_series, scale=3, distribution='gamma'):\n", "    \"\"\"\n", "    Calcule l'indice SPI (Standardized Precipitation Index)\n", "    \n", "    Parameters:\n", "    -----------\n", "    precip_series : pandas.Series\n", "        Série temporelle des précipitations mensuelles\n", "    scale : int\n", "        <PERSON><PERSON><PERSON> en mois (1, 3, 6, 12)\n", "    distribution : str\n", "        Distribution statistique ('gamma' ou 'normal')\n", "        \n", "    Returns:\n", "    --------\n", "    spi_values : pandas.Series\n", "        Valeurs SPI correspondantes\n", "    \"\"\"\n", "    # Calcul des cumuls glissants\n", "    if scale > 1:\n", "        rolling_precip = precip_series.rolling(window=scale, min_periods=scale).sum()\n", "    else:\n", "        rolling_precip = precip_series.copy()\n", "    \n", "    # Suppression des valeurs manquantes\n", "    valid_data = rolling_precip.dropna()\n", "    \n", "    if len(valid_data) < 30:  # Minimum 30 valeurs pour ajustement\n", "        return pd.Series(index=precip_series.index, dtype=float)\n", "    \n", "    # Ajustement de la distribution\n", "    if distribution == 'gamma':\n", "        # Ajustement loi gamma (plus appropriée pour précipitations)\n", "        params = stats.gamma.fit(valid_data, floc=0)\n", "        cdf_values = stats.gamma.cdf(rolling_precip, *params)\n", "    else:\n", "        # Ajustement loi normale\n", "        params = stats.norm.fit(valid_data)\n", "        cdf_values = stats.norm.cdf(rolling_precip, *params)\n", "    \n", "    # Transformation en SPI (quantiles de la loi normale standard)\n", "    # Éviter les valeurs extrêmes (0 et 1)\n", "    cdf_values = np.clip(cdf_values, 0.001, 0.999)\n", "    spi_values = stats.norm.ppf(cdf_values)\n", "    \n", "    return pd.Series(spi_values, index=rolling_precip.index)\n", "\n", "# Calcul SPI-3 pour toutes les stations\n", "print(\"⚙️ Calcul des indices SPI-3 en cours...\")\n", "\n", "spi_results = []\n", "for station in drought_data['station'].unique():\n", "    station_data = drought_data[drought_data['station'] == station].copy()\n", "    station_data = station_data.sort_values('date')\n", "    \n", "    # Calcul SPI-3\n", "    spi_3 = calculate_spi(station_data['precip_mm'], scale=3)\n", "    \n", "    # Ajout des résultats\n", "    station_data['spi_3'] = spi_3\n", "    spi_results.append(station_data)\n", "\n", "# Consolidation des résultats\n", "drought_data_spi = pd.concat(spi_results, ignore_index=True)\n", "\n", "print(\"✅ Calcul SPI terminé!\")\n", "\n", "# Classification des niveaux de sécheresse\n", "def classify_drought(spi_value):\n", "    \"\"\"Classifie le niveau de sécheresse selon la valeur SPI\"\"\"\n", "    if pd.isna(spi_value):\n", "        return '<PERSON><PERSON><PERSON>'\n", "    elif spi_value >= 0:\n", "        return 'Normal à humide'\n", "    elif spi_value >= -1:\n", "        return '<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>'\n", "    elif spi_value >= -1.5:\n", "        return '<PERSON><PERSON><PERSON><PERSON><PERSON> mod<PERSON>'\n", "    elif spi_value >= -2:\n", "        return '<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>'\n", "    else:\n", "        return '<PERSON><PERSON><PERSON><PERSON><PERSON> extrême'\n", "\n", "drought_data_spi['drought_category'] = drought_data_spi['spi_3'].apply(classify_drought)\n", "\n", "# Statistiques SPI par station\n", "print(\"\\n📊 STATISTIQUES SPI-3 PAR STATION (2016):\")\n", "spi_stats = drought_data_spi[drought_data_spi['date'].dt.year == 2016].groupby('station')['spi_3'].agg([\n", "    'min', 'mean', 'max', 'std'\n", "]).round(2)\n", "\n", "print(\"=\"*70)\n", "print(f\"{'Station':<15} {'Min':<8} {'Moyenne':<10} {'Max':<8} {'Écart-type':<10}\")\n", "print(\"=\"*70)\n", "for station, stats_row in spi_stats.iterrows():\n", "    print(f\"{station:<15} {stats_row['min']:<8} {stats_row['mean']:<10} {stats_row['max']:<8} {stats_row['std']:<10}\")\n", "\n", "# Identification des mois les plus secs en 2016\n", "drought_2016 = drought_data_spi[drought_data_spi['date'].dt.year == 2016]\n", "worst_drought = drought_2016.loc[drought_2016['spi_3'].idxmin()]\n", "\n", "print(f\"\\n🔥 SÉCHERESSE LA PLUS SÉVÈRE EN 2016:\")\n", "print(f\"Station: {worst_drought['station']}\")\n", "print(f\"Date: {worst_drought['date'].strftime('%B %Y')}\")\n", "print(f\"SPI-3: {worst_drought['spi_3']:.2f} ({classify_drought(worst_drought['spi_3'])})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.4 Visualisation des séries temporelles SPI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Graphiques des séries temporelles SPI\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "axes = axes.flatten()\n", "\n", "stations = drought_data_spi['station'].unique()\n", "colors = ['blue', 'green', 'red', 'orange']\n", "\n", "for i, station in enumerate(stations):\n", "    station_data = drought_data_spi[drought_data_spi['station'] == station]\n", "    \n", "    ax = axes[i]\n", "    \n", "    # Ligne SPI\n", "    ax.plot(station_data['date'], station_data['spi_3'], \n", "           color=colors[i], linewidth=2, label=f'SPI-3 {station}')\n", "    \n", "    # Zones colorées selon niveau de sécheresse\n", "    ax.axhspan(-1, 0, alpha=0.2, color='yellow', label='Sécheresse légère')\n", "    ax.axhspan(-1.5, -1, alpha=0.3, color='orange', label='Sécheresse modérée')\n", "    ax.axhspan(-2, -1.5, alpha=0.4, color='red', label='Sécheresse sévère')\n", "    ax.axhspan(-3, -2, alpha=0.5, color='darkred', label='Sécheresse extrême')\n", "    \n", "    # Ligne de référence\n", "    ax.axhline(0, color='black', linestyle='--', alpha=0.5)\n", "    \n", "    # Mise en évidence de 2016\n", "    data_2016 = station_data[station_data['date'].dt.year == 2016]\n", "    ax.scatter(data_2016['date'], data_2016['spi_3'], \n", "              color='red', s=50, zorder=5, alpha=0.8)\n", "    \n", "    ax.set_title(f'Évolution SPI-3 - {station}', fontweight='bold')\n", "    ax.set_ylabel('SPI-3')\n", "    ax.grid(True, alpha=0.3)\n", "    ax.set_ylim(-3, 3)\n", "    \n", "    if i >= 2:  # Axes du bas\n", "        ax.set_xlabel('Date')\n", "    \n", "    # Légende seulement pour le premier graphique\n", "    if i == 0:\n", "        ax.legend(loc='upper right', fontsize=8)\n", "\n", "plt.suptitle('Évolution des Indices SPI-3 - Nord Côte d\\'Ivoire\\n(Points rouges = Année 2016)', \n", "             fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Graphique comparatif 2016\n", "plt.figure(figsize=(14, 8))\n", "\n", "for i, station in enumerate(stations):\n", "    station_2016 = drought_data_spi[\n", "        (drought_data_spi['station'] == station) & \n", "        (drought_data_spi['date'].dt.year == 2016)\n", "    ]\n", "    \n", "    plt.plot(station_2016['date'], station_2016['spi_3'], \n", "            marker='o', linewidth=3, markersize=8, \n", "            label=station, color=colors[i])\n", "\n", "# Zones de sécheresse\n", "plt.axhspan(-1, 0, alpha=0.2, color='yellow', label='Sécheresse légère')\n", "plt.axhspan(-1.5, -1, alpha=0.3, color='orange', label='Sécheresse modérée')\n", "plt.axhspan(-2, -1.5, alpha=0.4, color='red', label='Sécheresse sévère')\n", "plt.axhspan(-3, -2, alpha=0.5, color='darkred', label='Sécheresse extrême')\n", "\n", "plt.axhline(0, color='black', linestyle='--', alpha=0.7)\n", "plt.xlabel('Mois 2016', fontsize=12)\n", "plt.ylabel('SPI-3', fontsize=12)\n", "plt.title('Comparaison SPI-3 par Station - Saison Agricole 2016\\n(Mai <PERSON> Octobre)', \n", "          fontsize=14, fontweight='bold')\n", "plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.grid(True, alpha=0.3)\n", "plt.ylim(-2.5, 1)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📈 Graphiques SPI générés avec succès!\")"]}], "metadata": {"kernelspec": {"display_name": "climada", "language": "python", "name": "climada"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}